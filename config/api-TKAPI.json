{"openapi": "3.0.3", "info": {"title": "TK SubSystem API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "TK AI Chat API", "description": "Tk Chat Controller"}, {"name": "TK API", "description": "Tk Controller"}, {"name": "TK Buyer Creator API", "description": "Tk Buyer Controller"}, {"name": "TK Creator API", "description": "Tk Creator Controller"}, {"name": "TK Creator View API", "description": "Tk Creator View Controller"}, {"name": "TK Email API", "description": "Tk Email Controller"}, {"name": "TK Media API", "description": "Tk Media Controller"}, {"name": "TK Openapi", "description": "Tk Open App Controller"}, {"name": "TK Openapi Product API", "description": "Tk Open Product Controller"}, {"name": "TK Param API", "description": "Tk Param Controller"}, {"name": "TK Product API", "description": "Tk Product Controller"}, {"name": "TK Prop API", "description": "Tk Prop Controller"}, {"name": "TK Search API", "description": "Tk Search Controller"}, {"name": "TK Shop API", "description": "Tk Shop Controller"}, {"name": "TK 公海 API", "description": "Tk Public Controller"}, {"name": "TK 店铺 API", "description": "Tk Open Shop Controller"}, {"name": "TK图片通用接口", "description": "Disk Image Controller"}, {"name": "TK数据团队达人API", "description": "TK Data Creator Controller"}, {"name": "TK运营团队API", "description": "Tk Operation Controller"}], "paths": {"/api/tk/image/upload": {"post": {"tags": ["DiskImageController"], "summary": "通用图片存储接口", "operationId": "tkImageUploadPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadGeneralImageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/buyer/creator/page": {"get": {"tags": ["TkBuyerController"], "summary": "分页查询团队买家", "operationId": "tkBuyerCreatorPageGet", "parameters": [{"name": "search", "in": "query", "description": "根据买家id或者handle精确查找", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "description": "店铺ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "tagIds", "in": "query", "description": "按标签id过滤", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], last_buy_time,handle,order_count,order_amount", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkBuyerVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/buyer/findBuyerIdsByHandles": {"get": {"tags": ["TkBuyerController"], "summary": "根据handle列表查询出对应的买家列表", "operationId": "tkBuyerFindBuyerIdsByHandlesGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "handles", "in": "query", "description": "handles", "required": true, "style": "form", "explode": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/buyer/findBuyerOrders": {"get": {"tags": ["TkBuyerController"], "summary": "查询某个tk买家的订单", "operationId": "tkBuyerFindBuyerOrdersGet", "parameters": [{"name": "buyerId", "in": "query", "description": "buyerId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkOrderVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/buyer/findLatestOrderNo": {"get": {"tags": ["TkBuyerController"], "summary": "获取某个分身数据库里最后一个订单的orderNo，为空表示数据库里没有订单", "operationId": "tkBuyerFindLatestOrderNoGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/buyer/syncOrder": {"post": {"tags": ["TkBuyerController"], "summary": "同步一个订单，如果 买家/订单/产品 信息不存在则创建，存在则更新", "operationId": "tkBuyerSyncOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/chat/generate": {"post": {"tags": ["TkChatController"], "summary": "用AI生成相似的描述", "operationId": "tkChatGeneratePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenaiChatGenerateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/chat/translate": {"post": {"tags": ["TkChatController"], "summary": "用AI生成相似的描述", "operationId": "tkChatTranslatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenaiChatTranslateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/akList": {"get": {"tags": ["TkController"], "summary": "查询当前用户可用的Openapi AK列表", "operationId": "tkAkListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«OpenapiAkDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/calcBuyTkPackOrderPrice": {"post": {"tags": ["TkController"], "summary": "计算购买TK套餐订单的价格", "operationId": "tkCalcBuyTkPackOrderPricePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkPackRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcPriceResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/calcRenewTkPackPrice": {"post": {"tags": ["TkController"], "summary": "计算续费TK套餐订单的价格", "operationId": "tkCalcRenewTkPackPricePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRenewTkPackRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcPriceResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/confirmDelivered": {"put": {"tags": ["TkController"], "summary": "确认已交付", "operationId": "tkConfirmDeliveredPut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/createBuyTkPackOrder": {"post": {"tags": ["TkController"], "summary": "创建购买TK套餐订单", "operationId": "tkCreateBuyTkPackOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyTkPackRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateBuyTkPackOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/createRenewTkPackOrder": {"post": {"tags": ["TkController"], "summary": "创建续费TK套餐订单", "operationId": "tkCreateRenewTkPackOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRenewTkPackRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/discountInfo": {"get": {"tags": ["TkController"], "summary": "获取折扣信息", "operationId": "tkDiscountInfoGet", "parameters": [{"name": "count", "in": "query", "description": "购买月份", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "packId", "in": "query", "description": "packId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DiscountsDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/flows": {"get": {"tags": ["TkController"], "summary": "查询TK所有流程", "operationId": "tkFlowsGet", "parameters": [{"name": "packId", "in": "query", "description": "购买的套餐ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "orderBy", "in": "query", "description": "orderBy", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkFlowDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/groups/{subSystemType}": {"get": {"tags": ["TkController"], "summary": "根据子系统类型，查询流程分组", "operationId": "tkGroupsBySubSystemTypeGet", "parameters": [{"name": "subSystemType", "in": "path", "description": "subSystemType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["TK"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkFlowGroupVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/hasTrialOrder": {"get": {"tags": ["TkController"], "summary": "是否购买的试用版", "operationId": "tkHasTrialOrderGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/isv-token": {"post": {"tags": ["TkController"], "summary": "用当前AK创建签名的token，用户访问持有当前AK的第三方系统", "description": "请在request payload中传入需要签名的信息。系统默认会添加userId和teamId（有传入时）放入签名体。请注意request payload不能超过500字符", "operationId": "tkIsvTokenPost", "parameters": [{"name": "accessKeyId", "in": "query", "description": "签名使用的AK", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "expireSeconds", "in": "query", "description": "超时时间，单位秒，取值范围：[120,7200]", "required": true, "style": "form", "schema": {"maximum": 7200, "exclusiveMaximum": false, "minimum": 120, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AkAccessToken»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/orders": {"get": {"tags": ["TkController"], "summary": "查询团队订单列表", "operationId": "tkOrdersGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "id", "in": "query", "description": "id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "serialNumber", "in": "query", "description": "serialNumber", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "orderType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}}, {"name": "payStatus", "in": "query", "description": "payStatus", "required": false, "style": "form", "schema": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}}, {"name": "from", "in": "query", "description": "from", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«OrderDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/pack/paramDef": {"get": {"tags": ["TkController"], "summary": "获取套餐参数类型", "operationId": "tkPackParamDefGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkPackParamVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/pack/params": {"get": {"tags": ["TkController"], "summary": "获取当前团队套餐参数", "operationId": "tkPackParamsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkPackParamDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/packWithDistributionCode": {"get": {"tags": ["TkController"], "summary": "根据推荐码查询流程套餐", "operationId": "tkPackWithDistributionCodeGet", "parameters": [{"name": "packId", "in": "query", "description": "套餐ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "distributionCode", "in": "query", "description": "优惠码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkPackWithDistributionVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/packs/{subSystemType}": {"get": {"tags": ["TkController"], "summary": "根据子系统类型，查询流程套餐", "operationId": "tkPacksBySubSystemTypeGet", "parameters": [{"name": "subSystemType", "in": "path", "description": "subSystemType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["TK"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkPackVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/packsWithDistributionCode/{subSystemType}": {"get": {"tags": ["TkController"], "summary": "根据优惠码查询流程套餐", "operationId": "tkPacksWithDistributionCodeBySubSystemTypeGet", "parameters": [{"name": "subSystemType", "in": "path", "description": "subSystemType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["TK"]}}, {"name": "distributionCode", "in": "query", "description": "优惠码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkPacksWithDistribution»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/presentIps": {"get": {"tags": ["TkController"], "summary": "返回系统可赠送IP列表", "operationId": "tkPresentIpsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkPresentIpVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/systemStatus": {"get": {"tags": ["TkController"], "summary": "查询当前团队状态", "operationId": "tkSystemStatusGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkSystemStatusVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/teamPacks": {"get": {"tags": ["TkController"], "summary": "获取已购买套餐", "operationId": "tkTeamPacksGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkTeamPackVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/tkDowngrade": {"put": {"tags": ["TkController"], "summary": "将当前TK团队降级为普通团队", "operationId": "tkTkDowngradePut", "parameters": [{"name": "confirm", "in": "query", "description": "confirm", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/communication/page": {"get": {"tags": ["TkCreatorController"], "summary": "查询通信记录", "operationId": "tkCommunicationPageGet", "parameters": [{"name": "creatorId", "in": "query", "description": "花漾达人库ID，不是TK库的ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactTimeFrom", "in": "query", "description": "交互时间开始", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTimeTo", "in": "query", "description": "交互时间截止", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkInteractionDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/avatarByHandle": {"get": {"tags": ["TkCreatorController"], "summary": "查询达人头像", "operationId": "tkCreatorAvatarByHandleGet", "parameters": [{"name": "handle", "in": "query", "description": "花漾达人ID", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/byHandle": {"get": {"tags": ["TkCreatorController"], "summary": "查询达人详情", "operationId": "tkCreatorByHandleGet", "parameters": [{"name": "handle", "in": "query", "description": "花漾达人ID", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkCreatorDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/delete": {"delete": {"tags": ["TkCreatorController"], "summary": "删除达人", "operationId": "tkCreatorDeleteDelete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/page": {"get": {"tags": ["TkCreatorController"], "summary": "分页查询团队达人", "operationId": "tkCreatorPageGet", "parameters": [{"name": "creatorId", "in": "query", "description": "精确匹配creator_id", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "handle", "in": "query", "description": "LIKE匹配handle", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "alias", "in": "query", "description": "LIKE匹配alias", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bio", "in": "query", "description": "LIKE批bio", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "location", "in": "query", "description": "精确匹配location", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "tagIds", "in": "query", "description": "按标签id过滤，当前团队或数据团队的标签均支持", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "filterByFavorite", "in": "query", "description": "按当前用户的收藏过滤", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}}, {"name": "shopId", "in": "query", "description": "店铺ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "includeDocument", "in": "query", "description": "是否加载document", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "description": "lastSyncTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "description": "lastSyncTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastInteractTimeFrom", "in": "query", "description": "lastInteractTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastInteractTimeTo", "in": "query", "description": "lastInteractTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/sync": {"post": {"tags": ["TkCreatorController"], "summary": "同步（创建）TK达人", "operationId": "tkCreatorSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkCreatorDocumentSyncRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkCreatorDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/syncV2": {"post": {"tags": ["TkCreatorController"], "summary": "同步（创建）TK达人V2", "operationId": "tkCreatorSyncV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkCreatorDocumentV2"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkCreatorDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/{creatorId}": {"get": {"tags": ["TkCreatorController"], "summary": "查询达人详情", "operationId": "tkCreatorByCreatorIdGet", "parameters": [{"name": "creatorId", "in": "path", "description": "花漾达人ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkCreatorDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/hasInteraction": {"get": {"tags": ["TkCreatorController"], "summary": "是否与达人产生过交互", "operationId": "tkHasInteractionGet", "parameters": [{"name": "shopId", "in": "query", "description": "店铺ID，为null时，从团队全局统计", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "handle", "in": "query", "description": "达人账号", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}}, {"name": "interactTimeFrom", "in": "query", "description": "interactTimeFrom", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactTimeTo", "in": "query", "description": "interactTimeTo", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/hasInteractions": {"post": {"tags": ["TkCreatorController"], "summary": "是否与达人产生过交互（批量）", "operationId": "tkHasInteractionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HasInteractionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«HasInteractionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/interaction": {"post": {"tags": ["TkCreatorController"], "summary": "添加交互记录", "operationId": "tkInteractionPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkInteractionVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/interaction/page": {"get": {"tags": ["TkCreatorController"], "summary": "查询交互记录", "operationId": "tkInteractionPageGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "花漾达人库ID，不是TK库的ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}}, {"name": "operatorId", "in": "query", "description": "操作者ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactTimeFrom", "in": "query", "description": "交互时间开始", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTimeTo", "in": "query", "description": "交互时间截止", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkInteractionDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/interaction/{id}": {"get": {"tags": ["TkCreatorController"], "summary": "查询交互记录详情", "operationId": "tkInteractionByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkInteractionDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/interactions": {"post": {"tags": ["TkCreatorController"], "summary": "添加交互记录（批量）", "operationId": "tkInteractionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTkInteractionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/latestInteraction": {"get": {"tags": ["TkCreatorController"], "summary": "查询与达人最近产生过交互", "operationId": "tkLatestInteractionGet", "parameters": [{"name": "shopId", "in": "query", "description": "店铺ID，为null时，从团队全局统计", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "handle", "in": "query", "description": "达人账号", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkInteractionDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/count": {"post": {"tags": ["TkCreatorViewController"], "summary": "查询团队达人数量", "operationId": "tkCreatorViewCountPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/download/{token}": {"get": {"tags": ["TkCreatorViewController"], "summary": "下载达人信息", "operationId": "tkCreatorViewDownloadByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/exportToken": {"post": {"tags": ["TkCreatorViewController"], "summary": "下载达人的token", "operationId": "tkCreatorViewExportTokenPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportTkCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/fields": {"get": {"tags": ["TkCreatorViewController"], "summary": "达人可显示字段元信息", "operationId": "tkCreatorViewFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/page": {"post": {"tags": ["TkCreatorViewController"], "summary": "分页查询团队达人（不返回总数）", "operationId": "tkCreatorViewPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTkCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/page-complex": {"post": {"tags": ["TkCreatorViewController"], "summary": "分页查询团队达人（高级查询）", "operationId": "tkCreatorViewPageComplexPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/page-simple": {"get": {"tags": ["TkCreatorViewController"], "summary": "分页查询团队达人（简洁版）", "operationId": "tkCreatorViewPageSimpleGet", "parameters": [{"name": "alias", "in": "query", "description": "alias", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "handle", "in": "query", "description": "handle", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "tagIds", "in": "query", "description": "按标签id过滤", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}}, {"name": "shopId", "in": "query", "description": "店铺ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "includeDocument", "in": "query", "description": "是否加载document", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "viewName", "in": "query", "description": "显示方案名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "filterByFavorite", "in": "query", "description": "按当前用户的收藏过滤", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "location", "in": "query", "description": "精确匹配location", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "mediaInit", "in": "query", "description": "mediaInit", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "basicInit", "in": "query", "description": "basicInit", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "description": "followerCntFrom", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "description": "followerCntTo", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator-view/page-simple-v2": {"post": {"tags": ["TkCreatorViewController"], "summary": "分页查询团队达人（简洁版）V2", "operationId": "tkCreatorViewPageSimpleV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCreatorSimpleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/exists": {"get": {"tags": ["TKDataCreatorController"], "summary": "判断达人是否存在", "operationId": "tkCreatorExistsGet", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/initInfo": {"put": {"tags": ["TKDataCreatorController"], "summary": "更新达人初始化信息", "operationId": "tkCreatorInitInfoPut", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "basicInit", "in": "query", "description": "basicInit", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "mediaInit", "in": "query", "description": "mediaInit", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTime", "in": "query", "description": "最后同步时间，格式：'yyyy-MM-dd HH:mm:ss'，不设置是就是现在", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/list": {"get": {"tags": ["TKDataCreatorController"], "summary": "分页查询团队达人V2", "operationId": "tkCreatorListGet", "parameters": [{"name": "location", "in": "query", "description": "精确匹配location", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "mediaInit", "in": "query", "description": "mediaInit", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "basicInit", "in": "query", "description": "basicInit", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "handle", "in": "query", "description": "handle", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "lastSyncTimeFrom", "in": "query", "description": "lastSyncTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "description": "lastSyncTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastMediaTimeFrom", "in": "query", "description": "lastMediaTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastMediaTimeTo", "in": "query", "description": "lastMediaTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email": {"post": {"tags": ["TkEmailController"], "summary": "添加邮件服务", "operationId": "tkEmailPost", "parameters": [{"name": "skipTest", "in": "query", "description": "skipTest", "required": false, "style": "form", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkEmailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkEmailDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/filterPending": {"post": {"tags": ["TkEmailController"], "summary": "过滤出有邮件任务正在调度或执行中的达人列表", "operationId": "tkEmailFilterPendingPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/job/{id}": {"get": {"tags": ["TkEmailController"], "summary": "申请执行邮件Job", "operationId": "tkEmailJobByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkEmailJobVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/job/{id}/result": {"put": {"tags": ["TkEmailController"], "summary": "更新发送邮件的结果", "operationId": "tkEmailJobByIdResultPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}}, {"name": "description", "in": "query", "description": "description", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/list": {"get": {"tags": ["TkEmailController"], "summary": "邮件配置列表", "operationId": "tkEmailListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkEmailDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/probe": {"post": {"tags": ["TkEmailController"], "summary": "探测邮件服务", "operationId": "tkEmailProbePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkEmailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/task": {"post": {"tags": ["TkEmailController"], "summary": "创建一个发送邮件任务", "operationId": "tkEmailTaskPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmailTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/email/{id}": {"put": {"tags": ["TkEmailController"], "summary": "修改邮件服务", "operationId": "tkEmailByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "skipTest", "in": "query", "description": "skipTest", "required": false, "style": "form", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkEmailDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkEmailDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["TkEmailController"], "summary": "删除邮箱", "operationId": "tkEmailByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/livestream-daily-trend": {"get": {"tags": ["TkMediaController"], "summary": "最近30天直播趋势", "operationId": "tkLivestreamDailyTrendGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "days", "in": "query", "description": "days", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreatorMediaTrendVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/media": {"post": {"tags": ["TkMediaController"], "summary": "上传一批视频（或直播）数据（店铺端，按天）", "operationId": "tkMediaPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadMediaItemRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/media/syncMediaFromDocument": {"get": {"tags": ["TkMediaController"], "summary": "触发达人document到stat数据同步", "operationId": "tkMediaSyncMediaFromDocumentGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/media/trigger-calc-daily": {"get": {"tags": ["TkMediaController"], "summary": "触发统计按天数据（店铺端数据）", "operationId": "tkMediaTriggerCalcDailyGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/media/trigger-calc-home": {"get": {"tags": ["TkMediaController"], "summary": "触发统计（媒体端数据）", "operationId": "tkMediaTriggerCalcHomeGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/media2": {"post": {"tags": ["TkMediaController"], "summary": "媒体端上传一批视频（或直播）数据", "operationId": "tkMedia2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadMedia2ItemRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/promote-daily-trend": {"get": {"tags": ["TkMediaController"], "summary": "最近30天带货趋势", "operationId": "tkPromoteDailyTrendGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "days", "in": "query", "description": "days", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreatorPromoteTrendVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/video-daily-trend": {"get": {"tags": ["TkMediaController"], "summary": "最近30天短视频趋势", "operationId": "tkVideoDailyTrendGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "days", "in": "query", "description": "days", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreatorMediaTrendVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/authorize/callback/{service_id}": {"get": {"tags": ["TkOpenAppController"], "summary": "app授权回调", "operationId": "tkOpenAuthorizeCallbackByService_idGet", "parameters": [{"name": "service_id", "in": "path", "description": "service_id", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "state", "in": "query", "description": "state", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/authorizeResult/{state}": {"get": {"tags": ["TkOpenAppController"], "summary": "查询授权结果", "operationId": "tkOpenAuthorizeResultByStateGet", "parameters": [{"name": "state", "in": "path", "description": "state", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkAuthorizeUrlVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/authorizeUrl": {"get": {"tags": ["TkOpenAppController"], "summary": "获取店铺的授权URL（24小时有效）", "operationId": "tkOpenAuthorizeUrlGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/notification/{service_id}": {"post": {"tags": ["TkOpenAppController"], "summary": "接收TK平台回调", "operationId": "tkOpenNotificationByService_idPost", "parameters": [{"name": "service_id", "in": "path", "description": "service_id", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/user/refreshToken": {"get": {"tags": ["TkOpenAppController"], "summary": "触发TK用户refreshToken（仅用于测试）", "operationId": "tkOpenUserRefreshTokenGet", "parameters": [{"name": "tkUserId", "in": "query", "description": "tkUserId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/product/remark": {"put": {"tags": ["TkOpenProductController"], "summary": "修改产品的备注", "operationId": "tkOpenProductRemarkPut", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "productId", "in": "query", "description": "productId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "remark", "in": "query", "description": "remark", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/product/search": {"post": {"tags": ["TkOpenProductController"], "summary": "搜索产品列表", "operationId": "tkOpenProductSearchPost", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "includeThumbUrl", "in": "query", "description": "includeThumbUrl", "required": false, "style": "form", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkProductsSearchRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkProductsSearchResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/open/bind-tk-shop": {"get": {"tags": ["TkOpenShopController"], "summary": "查询花漾账号绑定的TK店铺信息", "operationId": "tkOpenBindTkShopGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkOpenShopVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/operation/copy": {"post": {"tags": ["TkOperationController"], "summary": "达人复制", "description": "返回异步任务ID", "operationId": "tkOperationCopyPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkCopyToClientRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/tags": {"get": {"tags": ["TkOperationController"], "summary": "查询标签列表", "operationId": "tkTagsGet", "parameters": [{"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TagVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/params": {"get": {"tags": ["TkParamController"], "summary": "获取指定团队TK参数", "operationId": "tkParamsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkParamVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/tk-param": {"put": {"tags": ["TkParamController"], "summary": "修改团队TK参数", "operationId": "tkTkParamPut", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "value", "in": "query", "description": "value", "required": true, "style": "form", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkParamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/product/page": {"get": {"tags": ["TkProductController"], "summary": "分页查询TK商品", "operationId": "tkProductPageGet", "parameters": [{"name": "itemsSoldFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "itemsSoldTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "priceFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "priceTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "productNo", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "quantityFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "quantityTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Deactivated", "Deleted", "Draft", "Live", "Revieweing", "Suspended"]}}, {"name": "updateTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "updateTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkProductDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/product/sync": {"post": {"tags": ["TkProductController"], "summary": "同步TK商品", "operationId": "tkProductSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncProductRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/product/{id}/remark": {"put": {"tags": ["TkProductController"], "summary": "设置商品的备注", "operationId": "tkProductByIdRemarkPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "remark", "in": "query", "description": "remark", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/creator/{creatorId}/props": {"get": {"tags": ["TkPropController"], "summary": "查询达人自定义字段", "operationId": "tkCreatorByCreatorIdPropsGet", "parameters": [{"name": "creatorId", "in": "path", "description": "creatorId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorPropDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TkPropController"], "summary": "创建（或修改）自定义字段", "operationId": "tkCreatorByCreatorIdPropsPut", "parameters": [{"name": "creatorId", "in": "path", "description": "creatorId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCreatorPropRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/prop/define": {"post": {"tags": ["TkPropController"], "summary": "创建（或修改）自定义字段", "operationId": "tkPropDefinePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTkPropDefRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkPropDefDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["TkPropController"], "summary": "删除自定义字段", "operationId": "tkPropDefineDelete", "parameters": [{"name": "propName", "in": "query", "description": "propName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/prop/defines": {"get": {"tags": ["TkPropController"], "summary": "查询自定义字段的定义列表", "operationId": "tkPropDefinesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkPropDefDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/public/creator-view/page-complex": {"post": {"tags": ["TkPublicController"], "summary": "分页查询团队达人（高级查询）", "operationId": "tkPublicCreatorViewPageComplexPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/public/creator-view/page-simple-v2": {"post": {"tags": ["TkPublicController"], "summary": "分页查询公海达人（简洁版）V2", "operationId": "tkPublicCreatorViewPageSimpleV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCreatorSimpleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkCreatorDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/public/tags": {"get": {"tags": ["TkPublicController"], "summary": "查询公海达人标签列表", "operationId": "tkPublicTagsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TagVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/search/task": {"post": {"tags": ["TkSearchController"], "summary": "创建搜索任务", "operationId": "tkSearchTaskPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTkSearchTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkSearchTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/search/task/page": {"get": {"tags": ["TkSearchController"], "summary": "分页查询任务", "operationId": "tkSearchTaskPageGet", "parameters": [{"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TkSearchTaskVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/search/task/{id}/detail": {"get": {"tags": ["TkSearchController"], "summary": "查询任务详情", "operationId": "tkSearchTaskByIdDetailGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkSearchTaskDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/search/task/{id}/status": {"put": {"tags": ["TkSearchController"], "summary": "更新任务状态", "operationId": "tkSearchTaskByIdStatusPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}}, {"name": "message", "in": "query", "description": "message", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkSearchTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/search/taskResult": {"post": {"tags": ["TkSearchController"], "summary": "追加搜索记录", "operationId": "tkSearchTaskResultPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddSearchTaskResultRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkSearchResultDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/shop/": {"post": {"tags": ["TkShopController"], "summary": "导入TK店铺", "operationId": "tkShopPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkShopImportParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/shop/areaListByCoordinateId/{coordinateId}": {"get": {"tags": ["TkShopController"], "summary": "根据协调ID查询所有店铺的区域列表", "operationId": "tkShopAreaListByCoordinateIdByCoordinateIdGet", "parameters": [{"name": "coordinateId", "in": "path", "description": "coordinateId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/shop/byCoordinateId/{coordinateId}": {"get": {"tags": ["TkShopController"], "summary": "根据协调ID查询所有店铺列表", "operationId": "tkShopByCoordinateIdByCoordinateIdGet", "parameters": [{"name": "coordinateId", "in": "path", "description": "coordinateId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/shop/byPlatforms": {"post": {"tags": ["TkShopController"], "summary": "扩展店铺的区域（新增其他区域的店铺）", "operationId": "tkShopByPlatformsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkShopAddRegionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/tk/shop/presentIp": {"post": {"tags": ["TkShopController"], "summary": "创建赠送IP", "operationId": "tkShopPresentIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePresentIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddSearchTaskResultRequest": {"title": "AddSearchTaskResultRequest", "type": "object", "properties": {"creatorAvatarBase64": {"type": "string", "description": "达人头像base64"}, "creatorAvatarBase64Ext": {"type": "string"}, "extraInfo": {"type": "object", "description": "额外信息（json）"}, "handle": {"type": "string", "description": "达人ID"}, "imageBase64": {"type": "string", "description": "封面图base64"}, "imageBase64Ext": {"type": "string"}, "name": {"type": "string", "description": "视频标题"}, "releaseTimestamp": {"type": "integer", "description": "发布时间", "format": "int64"}, "score": {"type": "number", "description": "评分", "format": "double"}, "taskId": {"type": "integer", "description": "所属任务ID", "format": "int64"}, "videoUrl": {"type": "string", "description": "视频下载地址"}, "viewCnt": {"type": "integer", "description": "播放量", "format": "int32"}}}, "AddTkInteractionRequest": {"title": "AddTkInteractionRequest", "type": "object", "properties": {"interactions": {"type": "array", "items": {"$ref": "#/components/schemas/TkInteractionVo"}}}}, "AkAccessToken": {"title": "AkAccessToken", "type": "object", "properties": {"expireTime": {"type": "string", "description": "Token超时时间", "format": "date-time"}, "token": {"type": "string", "description": "登录或请求的token"}}}, "BankPayConfig": {"title": "BankPayConfig", "type": "object", "properties": {"accountName": {"type": "string"}, "accountNumber": {"type": "string"}, "bankName": {"type": "string"}}}, "CalcPriceResponse": {"title": "CalcPriceResponse", "type": "object", "properties": {"discount": {"type": "number", "description": "折扣,[0-1]", "format": "double"}, "discountAmount": {"type": "number", "description": "打折减掉的金额(如果是打折的话)", "format": "bigdecimal"}, "items": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ItemPriceInfo"}, "description": "记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo>"}, "payablePrice": {"type": "number", "description": "订单应付价(减掉了打折等信息)", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送金额，目前只出现在购买花瓣", "format": "bigdecimal"}, "totalCost": {"type": "number", "description": "订单总成本", "format": "bigdecimal"}, "totalPrice": {"type": "number", "description": "订单总价(原价)", "format": "bigdecimal"}}}, "CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CreateBuyTkPackOrderResponse": {"title": "CreateBuyTkPackOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "team": {"description": "为订单创建的临时团队", "$ref": "#/components/schemas/TeamDto"}}}, "CreateBuyTkPackRequest": {"title": "CreateBuyTkPackRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "distributionCode": {"type": "string", "description": "优惠码"}, "distributionInfo": {"$ref": "#/components/schemas/DistributionInfo"}, "duration": {"type": "integer", "description": "购买时长，根据 periodUnit的值 有可能是月，周或天", "format": "int32"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "packId": {"type": "integer", "description": "TK套餐对应的套餐", "format": "int64"}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "trial": {"type": "boolean", "description": "是否使用套餐", "example": false}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "CreateCreatorPropRequest": {"title": "CreateCreatorPropRequest", "type": "object", "properties": {"props": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorPropVo"}}}}, "CreateEmailTaskRequest": {"title": "CreateEmailTaskRequest", "type": "object", "properties": {"documentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "emailId": {"type": "integer", "format": "int64"}, "sendList": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorEmailVo"}}}}, "CreateOrderResponse": {"title": "CreateOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "CreatePresentIpRequest": {"title": "CreatePresentIpRequest", "type": "object", "properties": {"bindPresentIp": {"type": "boolean", "description": "是否绑定赠送的IP", "example": false}, "presentIpId": {"type": "integer", "description": "赠送IP 0表示拒绝（不再赠送），null表示未赠送", "format": "int64"}, "shopIds": {"type": "array", "description": "需要绑定IP的店铺Ids", "items": {"type": "integer", "format": "int64"}}}}, "CreateRenewTkPackRequest": {"title": "CreateRenewTkPackRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "distributionCode": {"type": "string", "description": "优惠码"}, "distributionInfo": {"$ref": "#/components/schemas/DistributionInfo"}, "duration": {"type": "integer", "description": "购买时长，根据 periodUnit的值 有可能是月，周或天", "format": "int32"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "latestPrice": {"type": "boolean", "description": "按最新报价计算", "example": false}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "teamPackId": {"type": "integer", "description": "已购TK套餐对应的套餐", "format": "int64"}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "CreateTkPropDefRequest": {"title": "CreateTkPropDefRequest", "type": "object", "properties": {"defaultValue": {"type": "string", "description": "缺省值"}, "defineVo": {"type": "object", "description": "高级设置"}, "propName": {"type": "string", "description": "字段名称"}, "propType": {"type": "string", "description": "字段类型", "enum": ["list", "number", "text"]}}}, "CreateTkSearchTaskRequest": {"title": "CreateTkSearchTaskRequest", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "imageBase64": {"type": "string", "description": "产品图base64"}, "imageBase64Ext": {"type": "string"}, "maxTime": {"type": "integer", "format": "int32"}, "minScore": {"type": "integer", "format": "int32"}, "minTime": {"type": "integer", "format": "int32"}, "platforms": {"type": "string"}, "query": {"type": "string", "description": "关键词"}, "remark": {"type": "string"}}}, "CreatorEmailVo": {"title": "CreatorEmailVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "email": {"type": "string"}}}, "CreatorMediaTrendDailyItem": {"title": "CreatorMediaTrendDailyItem", "type": "object", "properties": {"commentCnt": {"type": "integer", "description": "频率数", "format": "int32"}, "day": {"type": "string", "format": "date-time"}, "likeCnt": {"type": "integer", "description": "点赞数", "format": "int32"}, "mediaList": {"type": "array", "description": "视频列表", "items": {"$ref": "#/components/schemas/TkMediaDto"}}, "viewCnt": {"type": "integer", "description": "播放量", "format": "int32"}, "viewerCnt": {"type": "integer", "description": "观众数", "format": "int32"}}}, "CreatorMediaTrendVo": {"title": "CreatorMediaTrendVo", "type": "object", "properties": {"hasData": {"type": "boolean", "description": "包含数据", "example": false}, "stats": {"type": "array", "description": "按天数据", "items": {"$ref": "#/components/schemas/CreatorMediaTrendDailyItem"}}}}, "CreatorPromoteTrendDailyItem": {"title": "CreatorPromoteTrendDailyItem", "type": "object", "properties": {"buyerCnt": {"type": "integer", "format": "int32"}, "currencySymbol": {"type": "string"}, "day": {"type": "string", "format": "date-time"}, "mediaList": {"type": "array", "description": "视频列表", "items": {"$ref": "#/components/schemas/TkMediaDto"}}, "orderCnt": {"type": "integer", "format": "int32"}, "revenue": {"type": "number", "format": "double"}}}, "CreatorPromoteTrendVo": {"title": "CreatorPromoteTrendVo", "type": "object", "properties": {"hasData": {"type": "boolean", "description": "包含数据", "example": false}, "stats": {"type": "array", "description": "按天数据", "items": {"$ref": "#/components/schemas/CreatorPromoteTrendDailyItem"}}}}, "CreatorPropVo": {"title": "CreatorPropVo", "type": "object", "properties": {"propName": {"type": "string"}, "propValue": {"type": "string"}}}, "DiscountsDto": {"title": "DiscountsDto", "type": "object", "properties": {"amount": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "discountCode": {"type": "string"}, "discountRange": {"type": "string", "enum": ["ConsumeCredit", "Ip", "Recharge", "RechargeCredit", "RpaVoucher", "TkPack"]}, "discountType": {"type": "string", "enum": ["Discount", "LadderPrice", "Present"]}, "enabled": {"type": "boolean"}, "goodsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string"}, "threshold": {"type": "integer", "format": "int32"}}}, "DiscountsVo": {"title": "DiscountsVo", "type": "object", "properties": {"amount": {"type": "integer", "description": "赠送数量或折扣百分比或阶梯折扣百分比", "format": "int32"}, "discountCode": {"type": "string", "description": "打折code"}, "discountType": {"type": "string", "description": "打折还是赠送", "enum": ["Discount", "LadderPrice", "Present"]}, "periodUnit": {"type": "string", "description": "周期或数量单位", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string", "description": "备注"}, "threshold": {"type": "integer", "description": "期数或数量", "format": "int32"}}}, "DistributionCodeDto": {"title": "DistributionCodeDto", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "code": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "discountId": {"type": "integer", "format": "int64"}, "distributionType": {"type": "string", "enum": ["Deduction", "Discount", "Official"]}, "distributor": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "limited": {"type": "boolean"}, "name": {"type": "string"}, "systemDefault": {"type": "boolean"}, "usageCount": {"type": "integer", "format": "int32"}, "usedCount": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}, "validDays": {"type": "integer", "format": "int32"}}}, "DistributionInfo": {"title": "DistributionInfo", "type": "object", "properties": {"code": {"$ref": "#/components/schemas/DistributionCodeDto"}, "deductedPrice": {"type": "number", "format": "bigdecimal"}, "drawPrice": {"type": "number", "format": "bigdecimal"}}}, "DocumentDto": {"title": "DocumentDto", "type": "object", "properties": {"category": {"type": "string"}, "content": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "link": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Deleted", "Draft", "Released"]}, "teamId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["Email"]}, "updateTime": {"type": "string", "format": "date-time"}, "updaterId": {"type": "integer", "format": "int64"}}}, "ExportTkCreatorRequest": {"title": "ExportTkCreatorRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "props": {"type": "array", "items": {"type": "string"}}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "teamId": {"type": "integer", "format": "int64"}}}, "FingerprintTemplateDto": {"title": "FingerprintTemplateDto", "type": "object", "properties": {"browser": {"type": "string", "enum": ["Chrome", "Edge", "Safari"]}, "configId": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["<PERSON>tch", "Gen", "HyGen", "MKLong", "Temp", "Template", "Transfer"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "majorVersion": {"type": "integer", "description": "如果不为空则说明该模板生成的实例固定使用该版本号，否则使用随机版本", "format": "int32"}, "maxIns": {"type": "integer", "format": "int32"}, "md5sum": {"type": "string"}, "name": {"type": "string"}, "platform": {"type": "string", "enum": ["Android", "IOS", "Linux", "<PERSON>", "Windows"]}, "teamId": {"type": "integer", "format": "int64"}, "usedIns": {"type": "integer", "format": "int32"}}}, "GoodsDto": {"title": "GoodsDto", "type": "object", "properties": {"arch": {"type": "string"}, "bandwidth": {"type": "integer", "format": "int32"}, "buyoutPrice": {"type": "number", "format": "bigdecimal"}, "city": {"type": "string"}, "cost": {"type": "number", "format": "bigdecimal"}, "countryCode": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "currency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "dayCost": {"type": "number", "format": "bigdecimal"}, "dayPrice": {"type": "number", "format": "bigdecimal"}, "dayTraffic": {"type": "number", "format": "double"}, "description": {"type": "string"}, "disk": {"type": "integer", "format": "int32"}, "diskCategory": {"type": "string"}, "dynamic": {"type": "boolean"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "initialQuantity": {"type": "integer", "format": "int32"}, "instanceType": {"type": "string"}, "ipv6": {"type": "boolean"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mem": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "onSale": {"type": "string", "enum": ["Disabled", "Offline", "Online"]}, "perfLevel": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "platform": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string"}, "region": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "resourceId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "tcpfp": {"type": "boolean"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "updateTime": {"type": "string", "format": "date-time"}, "weekCost": {"type": "number", "format": "bigdecimal"}, "weekPrice": {"type": "number", "format": "bigdecimal"}, "weekTraffic": {"type": "number", "format": "double"}}}, "HasInteractionRequest": {"title": "HasInteractionRequest", "type": "object", "properties": {"handlers": {"type": "array", "items": {"type": "string"}}, "interactTimeFrom": {"type": "integer", "format": "int64"}, "interactTimeTo": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "shopId": {"type": "integer", "format": "int64"}}}, "HasInteractionVo": {"title": "HasInteractionVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}}}, "ItemPriceInfo": {"title": "ItemPriceInfo", "type": "object", "properties": {"costPrice": {"type": "number", "format": "bigdecimal"}, "currentValidEndTime": {"type": "string", "description": "当前过期时间", "format": "date-time"}, "discount": {"$ref": "#/components/schemas/DiscountsVo"}, "discountAmount": {"type": "number", "description": "打折减掉的金额，如果是打折的话", "format": "bigdecimal"}, "goodsId": {"type": "integer", "format": "int64"}, "payablePrice": {"type": "number", "description": "应付价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送数量，如果是赠送的话。目前只出现在购买花瓣", "format": "bigdecimal"}, "price": {"type": "number", "description": "item总价", "format": "bigdecimal"}, "validEndTime": {"type": "string", "description": "续费后到期时间", "format": "date-time"}}}, "LadderPriceRange": {"title": "LadderPriceRange", "type": "object", "properties": {"amount": {"type": "integer", "description": "阶梯折扣百分比（=原价*amount/100）", "format": "int32"}, "threshold": {"type": "integer", "description": "超过特定数量", "format": "int32"}}}, "OpenaiChatGenerateRequest": {"title": "OpenaiChatGenerateRequest", "type": "object", "properties": {"accessToken": {"type": "string"}, "aiProvider": {"type": "string"}, "count": {"type": "integer", "format": "int32"}, "lang": {"type": "string"}, "prompt": {"type": "string"}}}, "OpenaiChatTranslateRequest": {"title": "OpenaiChatTranslateRequest", "type": "object", "properties": {"accessToken": {"type": "string"}, "aiProvider": {"type": "string"}, "langList": {"type": "array", "items": {"type": "string"}}, "prompt": {"type": "string"}}}, "OpenapiAkDto": {"title": "OpenapiAkDto", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "akType": {"type": "string", "enum": ["Global", "Partner", "PortalTeamMember"]}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Deleted", "Disabled", "Enabled"]}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}}}, "OrderDetailVo": {"title": "OrderDetailVo", "type": "object", "properties": {"automatic": {"type": "boolean", "description": "是否为自动订单，例如自动续费订单", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "bankPayConfig": {"description": "如果是银行卡支付，则包含银行卡信息", "$ref": "#/components/schemas/BankPayConfig"}, "cashPayType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "discountReason": {"type": "string"}, "earnedPartner": {"type": "integer", "format": "int64"}, "earnedPartnerDto": {"description": "钱款入账的代理商信息", "$ref": "#/components/schemas/PartnerDto"}, "id": {"type": "integer", "format": "int64"}, "lockTime": {"type": "string", "description": "订单锁定时间;订单锁定之后60分钟不支付会被取消掉", "format": "date-time"}, "nickname": {"type": "string"}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "parentOrderId": {"type": "integer", "description": "如果父订单不为空说明该订单被选中合并支付了", "format": "int64"}, "payStatus": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payablePrice": {"type": "number", "description": "订单应付总额，比如说买一年打8折或者销售改价之后的价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "充值赠送金额;只在充值订单有效", "format": "bigdecimal"}, "productionRemarks": {"type": "string", "description": "生产备注"}, "productionStatus": {"type": "string", "description": "生产状态", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "realPrice": {"type": "number", "description": "实付金额，即现金支付金额;可开票金额以此为依据", "format": "bigdecimal"}, "salesReduction": {"type": "number", "description": "销售改价折现", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "totalPrice": {"type": "number", "description": "订单总额", "format": "bigdecimal"}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额", "format": "bigdecimal"}, "voucherCardNumber": {"type": "string", "description": "代金券卡号"}}}, "OrderItemDto": {"title": "OrderItemDto", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "discountReason": {"type": "string"}, "extraInfo": {"type": "string"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsPeriodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "itemPrice": {"type": "number", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "productionRemarks": {"type": "string"}, "productionStatus": {"type": "string", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "resourceId": {"type": "integer", "format": "int64"}, "resourceName": {"type": "string"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "teamId": {"type": "integer", "format": "int64"}}}, "PageCreatorRequest": {"title": "PageCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "basicInit": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "includeDocument": {"type": "boolean"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "lastInteractTimeFrom": {"type": "string", "format": "date-time"}, "lastInteractTimeTo": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "location": {"type": "string"}, "mediaInit": {"type": "boolean"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "queryList": {"type": "array", "items": {"$ref": "#/components/schemas/RangeQueryVo"}}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PageCreatorSimpleRequest": {"title": "PageCreatorSimpleRequest", "type": "object", "properties": {"alias": {"type": "string"}, "basicInit": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "followerQueryList": {"type": "array", "items": {"$ref": "#/components/schemas/QueryItemVo"}}, "handle": {"type": "string"}, "includeDocument": {"type": "boolean"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "location": {"type": "string"}, "mediaInit": {"type": "boolean"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "viewName": {"type": "string"}}}, "PageResult«OrderDetailVo»": {"title": "PageResult«OrderDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkBuyerVo»": {"title": "PageResult«TkBuyerVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkBuyerVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkCreatorDetailVo»": {"title": "PageResult«TkCreatorDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkCreatorDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkCreatorDto»": {"title": "PageResult«TkCreatorDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkCreatorDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkInteractionDetailVo»": {"title": "PageResult«TkInteractionDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkInteractionDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkProductDto»": {"title": "PageResult«TkProductDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkProductDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TkSearchTaskVo»": {"title": "PageResult«TkSearchTaskVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TkSearchTaskVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageTkCreatorRequest": {"title": "PageTkCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "followerQueryList": {"type": "array", "items": {"$ref": "#/components/schemas/QueryItemVo"}}, "handle": {"type": "string"}, "includeDocument": {"type": "boolean"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "lastInteractTimeFrom": {"type": "string", "format": "date-time"}, "lastInteractTimeTo": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "location": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "queryList": {"type": "array", "items": {"$ref": "#/components/schemas/RangeQueryVo"}}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PartnerDto": {"title": "PartnerDto", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankName": {"type": "string"}, "bankNo": {"type": "string"}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "fullName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "managerId": {"type": "integer", "format": "int64"}, "oemSupport": {"type": "boolean"}, "openapiSupport": {"type": "boolean"}, "organizedTeamAccountQuota": {"type": "integer", "format": "int32"}, "organizedTeamUserQuota": {"type": "integer", "format": "int32"}, "password": {"type": "string"}, "role": {"type": "string", "enum": ["Broker", "Organizer"]}, "shortName": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "QueryItemVo": {"title": "QueryItemVo", "type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "number", "format": "double"}}}, "RangeQueryVo": {"title": "RangeQueryVo", "type": "object", "properties": {"from": {"type": "number", "format": "double"}, "key": {"type": "string"}, "to": {"type": "number", "format": "double"}}}, "Resource": {"title": "Resource", "type": "object"}, "RpaFlowDto": {"title": "RpaFlowDto", "type": "object", "properties": {"attachmentSize": {"type": "integer", "format": "int64"}, "bizCode": {"type": "string"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dirty": {"type": "boolean"}, "execCount": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "flowShareCode": {"type": "string"}, "groupId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastExecTime": {"type": "string", "format": "date-time"}, "marketId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "numberVersion": {"type": "integer", "format": "int32"}, "packId": {"type": "integer", "format": "int64"}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionInner": {"type": "boolean"}, "sharedFlowId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Draft", "Published"]}, "supportConcurrent": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "tkFlowId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "RpaFlowGroupVo": {"title": "RpaFlowGroupVo", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "RpaFlowVo": {"title": "RpaFlowVo", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean", "description": "是否可读。针对分享流程和市场流程", "example": false}, "bizCode": {"type": "string"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dirty": {"type": "boolean"}, "expireTime": {"type": "string", "description": "过期时间，仅针对引用市场流程", "format": "date-time"}, "expired": {"type": "boolean", "description": "是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的", "example": false}, "extra": {"type": "object"}, "flowShareCode": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowGroupVo"}}, "id": {"type": "integer", "format": "int64"}, "marketId": {"type": "integer", "description": "对应的市场模板ID", "format": "int64"}, "marketLatestVersion": {"type": "string", "description": "如果是市场流程，显示市场流程的最新版本"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "numberVersion": {"type": "integer", "description": "数字版本号，会从1开始累加", "format": "int32"}, "platforms": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlatformVo"}}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionInner": {"type": "boolean"}, "shareFromTeamId": {"type": "integer", "format": "int64"}, "shareFromTeamName": {"type": "string"}, "shareLatestVersion": {"type": "string", "description": "如果是分享过来的流程，显示被分享的流程最新的版本"}, "sharedFlowId": {"type": "integer", "description": "不为空表示是他人分享的流程", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Draft", "Published"]}, "supportConcurrent": {"type": "boolean"}, "teamId": {"type": "integer", "description": "团队ID;", "format": "int64"}, "teamName": {"type": "string"}, "tkFlowId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "RpaPlatformVo": {"title": "RpaPlatformVo", "type": "object", "properties": {"flowId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}}}, "RpaTaskDto": {"title": "RpaTaskDto", "type": "object", "properties": {"clientId": {"type": "string"}, "clientIp": {"type": "string"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creditDetailId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "deviceName": {"type": "string"}, "errorCode": {"type": "integer", "format": "int32"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "extra": {"type": "string"}, "fileLocked": {"type": "boolean"}, "fileStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "forceRecord": {"type": "boolean"}, "heartbeatTime": {"type": "string", "format": "date-time"}, "hyRuntimeId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "manualRun": {"type": "boolean"}, "name": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "planName": {"type": "string"}, "preview": {"type": "boolean"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}, "rpaVoucherId": {"type": "integer", "format": "int64"}, "runOnCloud": {"type": "boolean"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "teamId": {"type": "integer", "format": "int64"}}}, "ShopChatVo": {"title": "ShopChatVo", "type": "object", "properties": {"chatAccount": {"type": "string", "description": "沟通账号"}, "chatType": {"type": "string", "description": "沟通账号类型", "enum": ["None", "email", "facebookMessager", "line", "qq", "skype", "wechat", "whatsapp", "zalo"]}}}, "ShopDto": {"title": "ShopDto", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extension": {"type": "string", "enum": ["both", "extension", "huayang"]}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "shopNo": {"type": "string"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "webSecurity": {"type": "boolean"}}}, "ShopWithPlatformVo": {"title": "ShopWithPlatformVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "operatingCategory": {"type": "string", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "platformArea": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "platformId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "securityPolicyEnabled": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}}}, "SyncBuyerBean": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"avatar": {"type": "string", "description": "用户的头像地址"}, "cid": {"type": "string"}, "handle": {"type": "string"}}}, "SyncItemBean": {"title": "SyncItemBean", "type": "object", "properties": {"productName": {"type": "string"}, "productNo": {"type": "string"}, "productThumbUrl": {"type": "string", "description": "产品图片"}, "quantity": {"type": "integer", "description": "买了几个", "format": "int32"}, "unitPrice": {"type": "number", "description": "产品单价", "format": "bigdecimal"}}}, "SyncOrderBean": {"title": "SyncOrderBean", "type": "object", "properties": {"amount": {"type": "number", "description": "该订单付款总额", "format": "bigdecimal"}, "contactUrl": {"type": "string", "description": "给买家发消息的链接"}, "createTime": {"type": "string", "description": "这个订单的创建时间", "format": "date-time"}, "currency": {"type": "string", "description": "使用的是哪种货币，What your buyer paid那里可以看到"}, "location": {"type": "string", "description": "对应订单页面的Location字段"}, "orderNo": {"type": "string"}, "phone": {"type": "string"}, "shipAddress": {"type": "string"}}}, "SyncOrderRequest": {"title": "SyncOrderRequest", "type": "object", "properties": {"buyer": {"$ref": "#/components/schemas/SyncBuyerBean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/SyncItemBean"}}, "order": {"$ref": "#/components/schemas/SyncOrderBean"}, "shopId": {"type": "integer", "format": "int64"}}}, "SyncProductRequest": {"title": "SyncProductRequest", "type": "object", "properties": {"products": {"type": "array", "description": "产品列表", "items": {"$ref": "#/components/schemas/TkProductDocument"}}, "shopId": {"type": "integer", "description": "分身ID", "format": "int64"}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TagVo": {"title": "TagVo", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceCount": {"type": "integer", "description": "管理资源数", "format": "int32"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TaskDto": {"title": "TaskDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "detail": {"type": "string"}, "finishTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "remarks": {"type": "string"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "targetId": {"type": "integer", "format": "int64"}, "taskType": {"type": "string", "enum": ["BatchUpdateBandwidth", "CleanColdTable", "CleanMongo", "CleanTable", "CopyTkCreatorToClient", "CrsTransferOrder", "DbTransfer", "DeleteIps", "FireOpsMessage", "ImportAccounts", "ImportIp", "ImportIppIps", "ImportIps", "ImportShop", "OpenaiChatGenerate", "OpenaiChatTranslate", "ProbeBatchLaunchInstance", "ProbeIps", "RebootIp", "RefreshClash", "RefreshExtensions", "RepairGhLiveTime", "RepairKolLiveRate", "RepairKolLiveTime", "RepairOps", "RepairTkCreatorFollower", "ResetJdEip", "ReviseIpHostLocation", "ShardTableOps", "ShardTeamTableSql", "SshChangePort", "SshCommands", "SshCommandsBatchLaunchInstance", "SyncKolCreator", "SyncKolRegionMap", "TkSendEmail", "TransferShardTable", "TransferTable", "TransferTagResource", "TransferTkCreator", "UpgradeGhMessage", "UploadAiKnowledge", "UploadDiskFile", "UserRefreshIp"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamQuotaVo": {"title": "TeamQuotaVo", "type": "object", "properties": {"freeQuota": {"type": "number", "description": "团队免费配额，null表示使用官方默认，-1表示无限", "format": "double"}, "ladderPrices": {"type": "array", "description": "阶梯价格", "items": {"$ref": "#/components/schemas/LadderPriceRange"}}, "price": {"type": "number", "description": "官方单价（花瓣/天），0表示免费使用", "format": "double"}, "quota": {"type": "number", "description": "（最大）配额(-1表示不限制）", "format": "double"}, "quotaDesc": {"type": "string", "description": "配额描述"}, "quotaName": {"type": "string", "description": "配额名称", "enum": ["ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "TkAuthorizeUrlVo": {"title": "TkAuthorizeUrlVo", "type": "object", "properties": {"nickname": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "shopName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}, "tkShops": {"type": "array", "items": {"$ref": "#/components/schemas/TkOpenShopDto"}}, "userId": {"type": "integer", "format": "int64"}}}, "TkBuyerVo": {"title": "TkBuyerVo", "type": "object", "properties": {"avatar": {"type": "string"}, "cid": {"type": "string"}, "contactUrl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string"}, "fb": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastBuyTime": {"type": "string", "description": "最后购买时间", "format": "date-time"}, "line": {"type": "string"}, "orderAmount": {"type": "number", "description": "订单总额", "format": "bigdecimal"}, "orderCount": {"type": "integer", "description": "总订单数", "format": "int32"}, "phone": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "viber": {"type": "string"}, "whatsapp": {"type": "string"}, "zalo": {"type": "string"}}}, "TkCopyToClientRequest": {"title": "TkCopyToClientRequest", "type": "object", "properties": {"areaCode": {"type": "string"}, "clientTeamId": {"type": "integer", "description": "TK客户团队ID", "format": "int64"}, "clientTeamPhone": {"type": "string", "description": "TK客户团队联系人手机号"}, "complexRequest": {"description": "高级查询条件", "$ref": "#/components/schemas/PageCreatorRequest"}, "count": {"type": "integer", "description": "达人总数", "format": "int32"}, "creatorIds": {"type": "array", "description": "当前页面选择的达人", "items": {"type": "integer", "format": "int64"}}, "simpleRequest": {"description": "简易查询条件", "$ref": "#/components/schemas/PageCreatorSimpleRequest"}}}, "TkCreatorDetailVo": {"title": "TkCreatorDetailVo", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "basicInit": {"type": "boolean"}, "bio": {"type": "string"}, "configId": {"type": "string"}, "creatorId": {"type": "string"}, "currencySymbol": {"type": "string"}, "document": {"$ref": "#/components/schemas/TkCreatorDocument"}, "email": {"type": "string"}, "firstInteractTime": {"type": "string", "format": "date-time"}, "firstInteraction": {"description": "首次交互", "$ref": "#/components/schemas/TkInteractionDto"}, "followerCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "gmvPerBuyer": {"type": "number", "format": "double"}, "gpm": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastInteractShop": {"type": "integer", "format": "int64"}, "lastInteractTime": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "lastInteraction": {"description": "最后一次交互", "$ref": "#/components/schemas/TkInteractionDto"}, "lastMediaTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "liveGpmMax": {"type": "number", "format": "double"}, "liveGpmMin": {"type": "number", "format": "double"}, "location": {"type": "string"}, "mediaInit": {"type": "boolean"}, "productCnt": {"type": "integer", "format": "int32"}, "propsMap": {"type": "object", "additionalProperties": {"type": "string"}, "description": "自定义字段"}, "shops": {"type": "array", "description": "交互的店铺", "items": {"$ref": "#/components/schemas/ShopDto"}}, "statInit": {"type": "boolean"}, "statsMap": {"type": "object", "additionalProperties": {"type": "number"}, "description": "统计数据"}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "unitsSold": {"type": "number", "format": "double"}, "videoGpmMax": {"type": "number", "format": "double"}, "videoGpmMin": {"type": "number", "format": "double"}}}, "TkCreatorDocument": {"title": "TkCreatorDocument", "type": "object", "properties": {"contactInfo": {"type": "array", "description": "联系方式", "items": {"type": "object"}}, "creatorInfo": {"type": "object", "description": "达人基本信息"}, "id": {"type": "string", "description": "id"}, "statsMap": {"type": "object", "description": "达人统计信息"}, "video": {"type": "array", "description": "热门视频", "items": {"type": "object"}}}}, "TkCreatorDocumentSyncRequest": {"title": "TkCreatorDocumentSyncRequest", "type": "object", "properties": {"avatarImageBase64": {"type": "string", "description": "头像数据base64编码"}, "avatarImageExt": {"type": "string", "description": "头像扩展名"}, "contactInfo": {"type": "array", "description": "联系方式", "items": {"type": "object"}}, "creatorInfo": {"type": "object", "description": "达人基本信息"}, "id": {"type": "string", "description": "id"}, "statsMap": {"type": "object", "description": "达人统计信息"}, "video": {"type": "array", "description": "热门视频", "items": {"type": "object"}}}}, "TkCreatorDocumentV2": {"title": "TkCreatorDocumentV2", "type": "object", "properties": {"avatarImageBase64": {"type": "string", "description": "头像数据base64编码"}, "avatarImageExt": {"type": "string", "description": "头像扩展名"}, "creator_id": {"type": "string"}, "creator_oec_id": {"type": "string", "description": "CID"}, "location": {"type": "string", "description": "区域"}, "nick_name": {"type": "string"}, "user_name": {"type": "string", "description": "handle"}}}, "TkCreatorDto": {"title": "TkCreatorDto", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "basicInit": {"type": "boolean"}, "bio": {"type": "string"}, "configId": {"type": "string"}, "creatorId": {"type": "string"}, "currencySymbol": {"type": "string"}, "email": {"type": "string"}, "firstInteractTime": {"type": "string", "format": "date-time"}, "followerCnt": {"type": "integer", "format": "int32"}, "gmv": {"type": "number", "format": "double"}, "gmvPerBuyer": {"type": "number", "format": "double"}, "gpm": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastInteractShop": {"type": "integer", "format": "int64"}, "lastInteractTime": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "lastMediaTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "liveGpmMax": {"type": "number", "format": "double"}, "liveGpmMin": {"type": "number", "format": "double"}, "location": {"type": "string"}, "mediaInit": {"type": "boolean"}, "productCnt": {"type": "integer", "format": "int32"}, "statInit": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "unitsSold": {"type": "number", "format": "double"}, "videoGpmMax": {"type": "number", "format": "double"}, "videoGpmMin": {"type": "number", "format": "double"}}}, "TkCreatorFiledVo": {"title": "TkCreatorFiledVo", "type": "object", "properties": {"desc": {"type": "string", "description": "字段描述（可能为null）"}, "filedType": {"type": "string", "enum": ["CreatorColumn", "CreatorContact", "CreatorProp", "CreatorStat", "ProductColumn"]}, "force": {"type": "boolean", "description": "方案必须包含", "example": false}, "key": {"type": "string", "description": "字段Key"}, "label": {"type": "string", "description": "字段名称"}, "path": {"type": "string"}}}, "TkCreatorPropDto": {"title": "TkCreatorPropDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "propName": {"type": "string"}, "propValue": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkEmailDto": {"title": "TkEmailDto", "type": "object", "properties": {"encryption": {"type": "string", "enum": ["PLAINTEXT", "SSL", "STARTTLS"]}, "host": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSendTime": {"type": "string", "format": "date-time"}, "maxPerDay": {"type": "integer", "format": "int32"}, "maxReceivers": {"type": "integer", "format": "int32"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "sendInterval": {"type": "integer", "format": "int32"}, "senderName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "workingRange": {"type": "string"}}}, "TkEmailJobVo": {"title": "TkEmailJobVo", "type": "object", "properties": {"creator": {"$ref": "#/components/schemas/TkCreatorDto"}, "document": {"$ref": "#/components/schemas/DocumentDto"}, "email": {"$ref": "#/components/schemas/TkEmailDto"}, "id": {"type": "integer", "format": "int64"}, "targetEmail": {"type": "string"}}}, "TkFlowDetailVo": {"title": "TkFlowDetailVo", "type": "object", "properties": {"bizCode": {"type": "string"}, "configId": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "draftConfigId": {"type": "string"}, "flowGroupName": {"type": "string"}, "groupId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastPublishTime": {"type": "string", "format": "date-time"}, "lastVersion": {"type": "string"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "platformType": {"type": "string"}, "rpaFlow": {"description": "如果当前套餐包含此流程，则返回购买后的RPA流程", "$ref": "#/components/schemas/RpaFlowDto"}, "sessionInner": {"type": "boolean"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Disabled", "Enabled", "Offline"]}, "subSystemType": {"type": "string", "enum": ["TK"]}, "supportConcurrent": {"type": "boolean"}, "tkGroup": {"description": "TK流程的TK分組", "$ref": "#/components/schemas/TkFlowGroupDto"}, "uiShow": {"type": "boolean"}}}, "TkFlowGroupDto": {"title": "TkFlowGroupDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "subSystemType": {"type": "string", "enum": ["TK"]}}}, "TkFlowGroupVo": {"title": "TkFlowGroupVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "flows": {"type": "array", "items": {"$ref": "#/components/schemas/TkFlowVo"}}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "subSystemType": {"type": "string", "enum": ["TK"]}}}, "TkFlowVo": {"title": "TkFlowVo", "type": "object", "properties": {"bizCode": {"type": "string"}, "configId": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "draftConfigId": {"type": "string"}, "flowGroupName": {"type": "string"}, "groupId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastPublishTime": {"type": "string", "format": "date-time"}, "lastVersion": {"type": "string"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "packFlows": {"type": "array", "items": {"$ref": "#/components/schemas/TkPackFlowDto"}}, "platformType": {"type": "string"}, "sessionInner": {"type": "boolean"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Disabled", "Enabled", "Offline"]}, "subSystemType": {"type": "string", "enum": ["TK"]}, "supportConcurrent": {"type": "boolean"}, "uiShow": {"type": "boolean"}}}, "TkInteractionDetailVo": {"title": "TkInteractionDetailVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "extraInfo": {"type": "object"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "interactionId": {"type": "string"}, "operator": {"$ref": "#/components/schemas/UserDto"}, "operatorId": {"type": "integer", "format": "int64"}, "rpaFlow": {"$ref": "#/components/schemas/RpaFlowDto"}, "rpaTask": {"$ref": "#/components/schemas/RpaTaskDto"}, "shop": {"$ref": "#/components/schemas/ShopDto"}, "shopId": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkInteractionDto": {"title": "TkInteractionDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "interactionId": {"type": "string"}, "operatorId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkInteractionVo": {"title": "TkInteractionVo", "type": "object", "properties": {"creatorDbId": {"type": "integer", "description": "达人数据库ID", "format": "int64"}, "creatorId": {"type": "string", "description": "达人ID"}, "description": {"type": "string", "description": "交互的描述"}, "extraInfo": {"type": "object", "description": "额外信息，转化成JSON总长度不要超过8000"}, "flowId": {"type": "integer", "description": "操作流程", "format": "int64"}, "handle": {"type": "string", "description": "达人账号"}, "interactTimestamp": {"type": "integer", "description": "交互时间戳", "format": "int64"}, "interactType": {"type": "string", "description": "交互类型", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "interactionId": {"type": "string", "description": "交互D"}, "shopId": {"type": "integer", "description": "店铺ID", "format": "int64"}, "success": {"type": "boolean", "description": "是否成功", "example": false}, "taskId": {"type": "integer", "description": "流程任务ID", "format": "int64"}}}, "TkMediaDto": {"title": "TkMediaDto", "type": "object", "properties": {"buyerCnt": {"type": "integer", "format": "int32"}, "coRate": {"type": "number", "format": "double"}, "commentCnt": {"type": "integer", "format": "int32"}, "creatorId": {"type": "integer", "format": "int64"}, "currencySymbol": {"type": "string"}, "duration": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "likeCnt": {"type": "integer", "format": "int32"}, "mediaId": {"type": "string"}, "mediaType": {"type": "string", "enum": ["livestream", "video"]}, "name": {"type": "string"}, "orderCnt": {"type": "integer", "format": "int32"}, "postUrl": {"type": "string"}, "productImpressions": {"type": "integer", "format": "int32"}, "releaseTime": {"type": "string", "format": "date-time"}, "revenue": {"type": "number", "format": "double"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "viewCnt": {"type": "integer", "format": "int32"}, "viewerCnt": {"type": "integer", "format": "int32"}}}, "TkOpenShopDto": {"title": "TkOpenShopDto", "type": "object", "properties": {"appId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "importTime": {"type": "string", "format": "date-time"}, "openUserId": {"type": "integer", "format": "int64"}, "shopCipher": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "tkRegion": {"type": "string"}, "tkShopId": {"type": "string"}, "tkShopName": {"type": "string"}, "tkShopType": {"type": "integer", "format": "int32"}}}, "TkOpenShopVo": {"title": "TkOpenShopVo", "type": "object", "properties": {"appId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "importTime": {"type": "string", "format": "date-time"}, "openUser": {"$ref": "#/components/schemas/TkOpenUserDto"}, "openUserId": {"type": "integer", "format": "int64"}, "shopCipher": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "tkRegion": {"type": "string"}, "tkShopId": {"type": "string"}, "tkShopName": {"type": "string"}, "tkShopType": {"type": "integer", "format": "int32"}}}, "TkOpenUserDto": {"title": "TkOpenUserDto", "type": "object", "properties": {"accessToken": {"type": "string"}, "accessTokenExpireIn": {"type": "string", "format": "date-time"}, "appId": {"type": "integer", "format": "int64"}, "authorized": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "deauthorizedTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "lastRefreshTime": {"type": "string", "format": "date-time"}, "message": {"type": "string"}, "openId": {"type": "string"}, "refreshToken": {"type": "string"}, "refreshTokenExpireIn": {"type": "string", "format": "date-time"}, "sellerBaseRegion": {"type": "string"}, "sellerName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "tkUserType": {"type": "string", "enum": ["Creator", "None", "<PERSON><PERSON>"]}, "userId": {"type": "integer", "format": "int64"}}}, "TkOrderItemVo": {"title": "TkOrderItemVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "orderId": {"type": "integer", "format": "int64"}, "product": {"$ref": "#/components/schemas/TkProductVo"}, "productId": {"type": "integer", "format": "int64"}, "productNo": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "unitPrice": {"type": "number", "format": "bigdecimal"}}}, "TkOrderVo": {"title": "TkOrderVo", "type": "object", "properties": {"amount": {"type": "number", "description": "该订单总金额", "format": "bigdecimal"}, "buyerHandle": {"type": "string"}, "buyerId": {"type": "integer", "format": "int64"}, "contactUrl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "currency": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/TkOrderItemVo"}}, "lasSyncTime": {"type": "string", "format": "date-time"}, "location": {"type": "string", "description": "对应订单页面的Location属性"}, "orderNo": {"type": "string"}, "phone": {"type": "string"}, "shipAddress": {"type": "string"}, "shop": {"$ref": "#/components/schemas/ShopWithPlatformVo"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkPackFlowDto": {"title": "TkPackFlowDto", "type": "object", "properties": {"flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "packId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Disabled", "Enabled", "Offline"]}}}, "TkPackParamDto": {"title": "TkPackParamDto", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "packId": {"type": "integer", "format": "int64"}, "paramKey": {"type": "string"}, "paramValue": {"type": "integer", "format": "int32"}}}, "TkPackParamVo": {"title": "TkPackParamVo", "type": "object", "properties": {"label": {"type": "string"}, "paramKey": {"type": "string"}}}, "TkPackVo": {"title": "TkPackVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "customDevelopment": {"type": "boolean"}, "goods": {"description": "对应商品", "$ref": "#/components/schemas/GoodsDto"}, "id": {"type": "integer", "format": "int64"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "name": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "套餐参数:paramKey=>value"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "presentCredit": {"type": "integer", "format": "int32"}, "presentIpEnabled": {"type": "boolean"}, "realPrice": {"type": "number", "format": "bigdecimal"}, "shopQuota": {"type": "integer", "format": "int32"}, "sortNo": {"type": "integer", "format": "int32"}, "subSystemType": {"type": "string", "enum": ["TK"]}, "trailDays": {"type": "integer", "format": "int32"}, "trailEnabled": {"type": "boolean"}, "trailPrice": {"type": "number", "format": "bigdecimal"}, "userQuota": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}}}, "TkPackWithDistributionVo": {"title": "TkPackWithDistributionVo", "type": "object", "properties": {"costPrice": {"type": "number", "description": "拿货价格", "format": "bigdecimal"}, "createTime": {"type": "string", "format": "date-time"}, "customDevelopment": {"type": "boolean"}, "deductedPrice": {"type": "number", "description": "扣减掉金额", "format": "bigdecimal"}, "distributionPrice": {"type": "number", "description": "优惠后价格", "format": "bigdecimal"}, "goods": {"description": "对应商品", "$ref": "#/components/schemas/GoodsDto"}, "id": {"type": "integer", "format": "int64"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "name": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "套餐参数:paramKey=>value"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "presentCredit": {"type": "integer", "format": "int32"}, "presentIpEnabled": {"type": "boolean"}, "realPrice": {"type": "number", "format": "bigdecimal"}, "shopQuota": {"type": "integer", "format": "int32"}, "sortNo": {"type": "integer", "format": "int32"}, "subSystemType": {"type": "string", "enum": ["TK"]}, "trailDays": {"type": "integer", "format": "int32"}, "trailEnabled": {"type": "boolean"}, "trailPrice": {"type": "number", "format": "bigdecimal"}, "userQuota": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}}}, "TkPacksWithDistribution": {"title": "TkPacksWithDistribution", "type": "object", "properties": {"distributionCode": {"$ref": "#/components/schemas/DistributionCodeDto"}, "packs": {"type": "array", "items": {"$ref": "#/components/schemas/TkPackWithDistributionVo"}}}}, "TkParamDto": {"title": "TkParamDto", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "maxValue": {"type": "number", "format": "double"}, "paramKey": {"type": "string"}, "paramValue": {"type": "number", "format": "double"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkParamVo": {"title": "TkParamVo", "type": "object", "properties": {"maxValue": {"type": "number", "format": "double"}, "paramKey": {"type": "string"}, "paramLabel": {"type": "string"}, "paramValue": {"type": "number", "format": "double"}}}, "TkPresentIpVo": {"title": "TkPresentIpVo", "type": "object", "properties": {"goods": {"$ref": "#/components/schemas/GoodsDto"}, "goodsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "platformId": {"type": "integer", "format": "int64"}}}, "TkProductDocument": {"title": "TkProductDocument", "type": "object", "properties": {"name": {"type": "string", "description": "产品名称"}, "no": {"type": "string", "description": "产品ID"}, "planEnabled": {"type": "boolean"}, "planRemark": {"type": "string"}, "price": {"type": "number", "description": "单价", "format": "bigdecimal"}, "priceUnit": {"type": "string", "description": "价格单位"}, "quantity": {"type": "integer", "description": "库存", "format": "int32"}, "status": {"type": "string", "description": "状态", "enum": ["Deactivated", "Deleted", "Draft", "Live", "Revieweing", "Suspended"]}, "thumbUrl": {"type": "string", "description": "产品图片"}, "updateTime": {"type": "integer", "description": "更新时间戳", "format": "int64"}}}, "TkProductDto": {"title": "TkProductDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "productNo": {"type": "string"}, "regions": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "thumbUrl": {"type": "string"}, "tocRemark": {"type": "string"}}}, "TkProductVo": {"title": "TkProductVo", "type": "object", "properties": {"create_time": {"type": "integer", "format": "int64"}, "id": {"type": "string"}, "name": {"type": "string"}, "sale_regions": {"type": "array", "items": {"type": "string"}}, "status": {"type": "integer", "format": "int32"}, "thumbUrl": {"type": "string"}, "tocRemark": {"type": "string"}, "update_time": {"type": "integer", "format": "int64"}}}, "TkProductsSearchRequest": {"title": "TkProductsSearchRequest", "type": "object", "properties": {"create_time_from": {"type": "integer", "format": "int64"}, "create_time_to": {"type": "integer", "format": "int64"}, "page_number": {"type": "integer", "format": "int32"}, "page_size": {"type": "integer", "format": "int32"}, "search_status": {"type": "integer", "format": "int32"}, "seller_sku_list": {"type": "string"}, "update_time_from": {"type": "integer", "format": "int64"}, "update_time_to": {"type": "integer", "format": "int64"}}}, "TkProductsSearchResult": {"title": "TkProductsSearchResult", "type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/TkProductVo"}}, "total": {"type": "integer", "format": "int32"}}}, "TkPropDefDto": {"title": "TkPropDefDto", "type": "object", "properties": {"defaultValue": {"type": "string"}, "defineJson": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "propName": {"type": "string"}, "propType": {"type": "string", "enum": ["list", "number", "text"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TkSearchResultDto": {"title": "TkSearchResultDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorAvatar": {"type": "string"}, "extraInfo": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "name": {"type": "string"}, "releaseTime": {"type": "string", "format": "date-time"}, "score": {"type": "number", "format": "double"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "videoUrl": {"type": "string"}, "viewCnt": {"type": "integer", "format": "int32"}}}, "TkSearchTaskDetailVo": {"title": "TkSearchTaskDetailVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "maxTime": {"type": "integer", "format": "int32"}, "minScore": {"type": "integer", "format": "int32"}, "minTime": {"type": "integer", "format": "int32"}, "platforms": {"type": "string"}, "query": {"type": "string"}, "remark": {"type": "string"}, "resultCount": {"type": "integer", "format": "int32"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/TkSearchResultDto"}}, "startTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "taskMessage": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkSearchTaskDto": {"title": "TkSearchTaskDto", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "maxTime": {"type": "integer", "format": "int32"}, "minScore": {"type": "integer", "format": "int32"}, "minTime": {"type": "integer", "format": "int32"}, "platforms": {"type": "string"}, "query": {"type": "string"}, "remark": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "taskMessage": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkSearchTaskVo": {"title": "TkSearchTaskVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "maxTime": {"type": "integer", "format": "int32"}, "minScore": {"type": "integer", "format": "int32"}, "minTime": {"type": "integer", "format": "int32"}, "platforms": {"type": "string"}, "query": {"type": "string"}, "remark": {"type": "string"}, "resultCount": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "taskMessage": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TkShopAddRegionRequest": {"title": "TkShopAddRegionRequest", "type": "object", "properties": {"platformIds": {"type": "array", "description": "新增的平台ID", "items": {"type": "integer", "format": "int64"}}, "shopId": {"type": "integer", "description": "原始店铺ID", "format": "int64"}}}, "TkShopImportParamVo": {"title": "TkShopImportParamVo", "type": "object", "properties": {"addPassword": {"type": "boolean", "description": "是否添加密码记录", "example": false}, "bindFingerprint": {"type": "boolean", "description": "是否绑定空闲指纹", "example": false}, "bindPresentIp": {"type": "boolean", "description": "是否绑定赠送的IP", "example": false}, "chats": {"type": "array", "description": "沟通账户", "items": {"$ref": "#/components/schemas/ShopChatVo"}}, "description": {"type": "string", "description": "描述"}, "extraProp": {"type": "string", "description": "额外属性"}, "ipId": {"type": "integer", "description": "绑定IP", "format": "int64"}, "name": {"type": "string", "description": "店铺名称"}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "password": {"type": "string", "description": "店铺分身密码"}, "platformIds": {"type": "array", "description": "平台ID", "items": {"type": "integer", "format": "int64"}}, "presentIpId": {"type": "integer", "description": "赠送IP 0表示拒绝（不再赠送），null表示未赠送", "format": "int64"}, "shopId": {"type": "integer", "description": "原本的店铺ID，追加区域", "format": "int64"}, "stateless": {"type": "boolean", "description": "是否创建无状态分身", "example": false}, "tagIds": {"type": "array", "description": "关联已有标签", "items": {"type": "integer", "format": "int64"}}, "tags": {"type": "array", "description": "创建标签", "items": {"type": "string"}}, "type": {"type": "string", "description": "店铺类型", "enum": ["Global", "Local", "None"]}, "username": {"type": "string", "description": "店铺分身用户名"}}}, "TkSystemStatusVo": {"title": "TkSystemStatusVo", "type": "object", "properties": {"fingerprintTemplate": {"description": "自动创建的指纹模版", "$ref": "#/components/schemas/FingerprintTemplateDto"}, "flows": {"type": "array", "description": "RPA流程", "items": {"$ref": "#/components/schemas/RpaFlowVo"}}, "order": {"description": "购买套餐的订单", "$ref": "#/components/schemas/OrderDetailVo"}, "orderItem": {"description": "购买套餐的订单条目", "$ref": "#/components/schemas/OrderItemDto"}, "shops": {"type": "array", "description": "创建的账号", "items": {"$ref": "#/components/schemas/ShopDto"}}, "team": {"description": "创建的团队信息", "$ref": "#/components/schemas/TeamDto"}, "teamQuotas": {"type": "array", "description": "团队配额信息", "items": {"$ref": "#/components/schemas/TeamQuotaVo"}}, "teamRelation": {"description": "TK团队关系对象", "$ref": "#/components/schemas/TkTeamRelationDto"}}}, "TkTeamPackVo": {"title": "TkTeamPackVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "customDevelopment": {"type": "boolean"}, "goodsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "name": {"type": "string"}, "packId": {"type": "integer", "format": "int64"}, "pauseTime": {"type": "string", "format": "date-time"}, "paused": {"type": "boolean"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "presentCredit": {"type": "integer", "format": "int32"}, "presentIpId": {"type": "integer", "format": "int64"}, "quotaMatch": {"type": "boolean"}, "realPrice": {"type": "number", "format": "bigdecimal"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "shopQuota": {"type": "integer", "format": "int32"}, "subSystemType": {"type": "string", "enum": ["TK"]}, "teamId": {"type": "integer", "format": "int64"}, "userQuota": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}}}, "TkTeamRelationDto": {"title": "TkTeamRelationDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "dataTeamId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "operationTeamId": {"type": "integer", "format": "int64"}, "tkRole": {"type": "string", "enum": ["TkClient", "TkData", "TkOperation"]}}}, "UploadGeneralImageRequest": {"title": "UploadGeneralImageRequest", "type": "object", "properties": {"ext": {"type": "string", "description": "图片的扩展名,默认png，可不设置"}, "id": {"type": "string", "description": "图片的ID"}, "imageBase64": {"type": "string", "description": "图片的base64编码"}, "publicAccess": {"type": "boolean", "description": "是否公共访问", "example": false}, "type": {"type": "string", "description": "图片的业务类型"}}}, "UploadMedia2ItemRequest": {"title": "UploadMedia2ItemRequest", "type": "object", "properties": {"handle": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/UploadMedia2ItemVo"}}}}, "UploadMedia2ItemVo": {"title": "UploadMedia2ItemVo", "type": "object", "properties": {"commentCnt": {"type": "integer", "description": "评论数", "format": "int32"}, "creatorId": {"type": "string", "description": "达人ID"}, "duration": {"type": "number", "description": "时长", "format": "double"}, "handle": {"type": "string", "description": "达人账号"}, "likeCnt": {"type": "integer", "description": "点赞数", "format": "int32"}, "mediaId": {"type": "string", "description": "视频或直播ID"}, "mediaType": {"type": "string", "description": "媒体类型", "enum": ["livestream", "video"]}, "name": {"type": "string", "description": "名称"}, "releaseTime": {"type": "integer", "description": "发布时间", "format": "int64"}, "viewCnt": {"type": "integer", "description": "观看人数", "format": "int32"}, "viewersCnt": {"type": "integer", "description": "观众数量", "format": "int32"}}}, "UploadMediaItemRequest": {"title": "UploadMediaItemRequest", "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UploadMediaItemVo"}}, "shopId": {"type": "integer", "format": "int64"}}}, "UploadMediaItemVo": {"title": "UploadMediaItemVo", "type": "object", "properties": {"buyerCnt": {"type": "integer", "format": "int32"}, "coRate": {"type": "number", "format": "double"}, "commentCnt": {"type": "integer", "description": "评论数", "format": "int32"}, "creatorId": {"type": "string", "description": "达人ID"}, "currencySymbol": {"type": "string", "description": "币种"}, "day": {"type": "string", "description": "日期,yyyy-MM-dd"}, "duration": {"type": "number", "description": "时长", "format": "double"}, "handle": {"type": "string", "description": "达人账号"}, "likeCnt": {"type": "integer", "description": "点赞数", "format": "int32"}, "mediaId": {"type": "string", "description": "视频或直播ID"}, "mediaType": {"type": "string", "description": "媒体类型", "enum": ["livestream", "video"]}, "name": {"type": "string", "description": "名称"}, "paidOrderCnt": {"type": "integer", "format": "int32"}, "postUrl": {"type": "string"}, "productImpressions": {"type": "integer", "format": "int32"}, "releaseTime": {"type": "integer", "description": "发布时间", "format": "int64"}, "revenue": {"type": "number", "format": "double"}, "viewCnt": {"type": "integer", "format": "int32"}, "viewersCnt": {"type": "integer", "format": "int32"}}}, "UserDto": {"title": "UserDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AkAccessToken»": {"title": "WebResult«AkAccessToken»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AkAccessToken"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CalcPriceResponse»": {"title": "WebResult«CalcPriceResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CalcPriceResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateBuyTkPackOrderResponse»": {"title": "WebResult«CreateBuyTkPackOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateBuyTkPackOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateOrderResponse»": {"title": "WebResult«CreateOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreatorMediaTrendVo»": {"title": "WebResult«CreatorMediaTrendVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreatorMediaTrendVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreatorPromoteTrendVo»": {"title": "WebResult«CreatorPromoteTrendVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreatorPromoteTrendVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DiscountsDto»": {"title": "WebResult«DiscountsDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DiscountsDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«HasInteractionVo»»": {"title": "WebResult«List«HasInteractionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/HasInteractionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«OpenapiAkDto»»": {"title": "WebResult«List«OpenapiAkDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OpenapiAkDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopDto»»": {"title": "WebResult«List«ShopDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TagVo»»": {"title": "WebResult«List«TagVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TagVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkCreatorFiledVo»»": {"title": "WebResult«List«TkCreatorFiledVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkCreatorFiledVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkCreatorPropDto»»": {"title": "WebResult«List«TkCreatorPropDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkCreatorPropDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkEmailDto»»": {"title": "WebResult«List«TkEmailDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkEmailDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkFlowDetailVo»»": {"title": "WebResult«List«TkFlowDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkFlowDetailVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkFlowGroupVo»»": {"title": "WebResult«List«TkFlowGroupVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkFlowGroupVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkOrderVo»»": {"title": "WebResult«List«TkOrderVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkOrderVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkPackParamDto»»": {"title": "WebResult«List«TkPackParamDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkPackParamDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkPackParamVo»»": {"title": "WebResult«List«TkPackParamVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkPackParamVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkPackVo»»": {"title": "WebResult«List«TkPackVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkPackVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkParamVo»»": {"title": "WebResult«List«TkParamVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkParamVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkPresentIpVo»»": {"title": "WebResult«List«TkPresentIpVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkPresentIpVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkPropDefDto»»": {"title": "WebResult«List«TkPropDefDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkPropDefDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkTeamPackVo»»": {"title": "WebResult«List«TkTeamPackVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkTeamPackVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,long»»": {"title": "WebResult«Map«string,long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«OrderDetailVo»»": {"title": "WebResult«PageResult«OrderDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«OrderDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkBuyerVo»»": {"title": "WebResult«PageResult«TkBuyerVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkBuyerVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkCreatorDetailVo»»": {"title": "WebResult«PageResult«TkCreatorDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkCreatorDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkCreatorDto»»": {"title": "WebResult«PageResult«TkCreatorDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkCreatorDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkInteractionDetailVo»»": {"title": "WebResult«PageResult«TkInteractionDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkInteractionDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkProductDto»»": {"title": "WebResult«PageResult«TkProductDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkProductDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TkSearchTaskVo»»": {"title": "WebResult«PageResult«TkSearchTaskVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TkSearchTaskVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TaskDto»": {"title": "WebResult«TaskDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TaskDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkAuthorizeUrlVo»": {"title": "WebResult«TkAuthorizeUrlVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkAuthorizeUrlVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkCreatorDetailVo»": {"title": "WebResult«TkCreatorDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkCreatorDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkCreatorDto»": {"title": "WebResult«TkCreatorDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkCreatorDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkEmailDto»": {"title": "WebResult«TkEmailDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkEmailDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkEmailJobVo»": {"title": "WebResult«TkEmailJobVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkEmailJobVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkInteractionDetailVo»": {"title": "WebResult«TkInteractionDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkInteractionDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkOpenShopVo»": {"title": "WebResult«TkOpenShopVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkOpenShopVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkPackWithDistributionVo»": {"title": "WebResult«TkPackWithDistributionVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkPackWithDistributionVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkPacksWithDistribution»": {"title": "WebResult«TkPacksWithDistribution»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkPacksWithDistribution"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkParamDto»": {"title": "WebResult«TkParamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkParamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkProductsSearchResult»": {"title": "WebResult«TkProductsSearchResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkProductsSearchResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkPropDefDto»": {"title": "WebResult«TkPropDefDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkPropDefDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkSearchResultDto»": {"title": "WebResult«TkSearchResultDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkSearchResultDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkSearchTaskDetailVo»": {"title": "WebResult«TkSearchTaskDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkSearchTaskDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkSearchTaskDto»": {"title": "WebResult«TkSearchTaskDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkSearchTaskDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkSystemStatusVo»": {"title": "WebResult«TkSystemStatusVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkSystemStatusVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}