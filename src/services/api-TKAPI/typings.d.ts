declare namespace API {
  type AddSearchTaskResultRequest = {
    /** 达人头像base64 */
    creatorAvatarBase64?: string;
    creatorAvatarBase64Ext?: string;
    /** 额外信息（json） */
    extraInfo?: Record<string, any>;
    /** 达人ID */
    handle?: string;
    /** 封面图base64 */
    imageBase64?: string;
    imageBase64Ext?: string;
    /** 视频标题 */
    name?: string;
    /** 发布时间 */
    releaseTimestamp?: number;
    /** 评分 */
    score?: number;
    /** 所属任务ID */
    taskId?: number;
    /** 视频下载地址 */
    videoUrl?: string;
    /** 播放量 */
    viewCnt?: number;
  };

  type AddTkInteractionRequest = {
    interactions?: TkInteractionVo[];
  };

  type AkAccessToken = {
    /** Token超时时间 */
    expireTime?: string;
    /** 登录或请求的token */
    token?: string;
  };

  type BankPayConfig = {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
  };

  type CalcPriceResponse = {
    /** 折扣,[0-1] */
    discount?: number;
    /** 打折减掉的金额(如果是打折的话) */
    discountAmount?: number;
    /** 记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo> */
    items?: Record<string, any>;
    /** 订单应付价(减掉了打折等信息) */
    payablePrice?: number;
    /** 赠送金额，目前只出现在购买花瓣 */
    presentAmount?: number;
    /** 订单总成本 */
    totalCost?: number;
    /** 订单总价(原价) */
    totalPrice?: number;
  };

  type CommonIdsRequest = {
    ids?: number[];
  };

  type CreateBuyTkPackOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
    /** 为订单创建的临时团队 */
    team?: TeamDto;
  };

  type CreateBuyTkPackRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 优惠码 */
    distributionCode?: string;
    distributionInfo?: DistributionInfo;
    /** 购买时长，根据 periodUnit的值 有可能是月，周或天 */
    duration?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    /** TK套餐对应的套餐 */
    packId?: number;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 是否使用套餐 */
    trial?: boolean;
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type CreateCreatorPropRequest = {
    props?: CreatorPropVo[];
  };

  type CreateEmailTaskRequest = {
    documentIds?: number[];
    emailId?: number;
    sendList?: CreatorEmailVo[];
  };

  type CreateOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
  };

  type CreatePresentIpRequest = {
    /** 是否绑定赠送的IP */
    bindPresentIp?: boolean;
    /** 赠送IP 0表示拒绝（不再赠送），null表示未赠送 */
    presentIpId?: number;
    /** 需要绑定IP的店铺Ids */
    shopIds?: number[];
  };

  type CreateRenewTkPackRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 优惠码 */
    distributionCode?: string;
    distributionInfo?: DistributionInfo;
    /** 购买时长，根据 periodUnit的值 有可能是月，周或天 */
    duration?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    /** 按最新报价计算 */
    latestPrice?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 已购TK套餐对应的套餐 */
    teamPackId?: number;
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type CreateTkPropDefRequest = {
    /** 缺省值 */
    defaultValue?: string;
    /** 高级设置 */
    defineVo?: Record<string, any>;
    /** 字段名称 */
    propName?: string;
    /** 字段类型 */
    propType?: 'list' | 'number' | 'text';
  };

  type CreateTkSearchTaskRequest = {
    count?: number;
    /** 产品图base64 */
    imageBase64?: string;
    imageBase64Ext?: string;
    maxTime?: number;
    minScore?: number;
    minTime?: number;
    platforms?: string;
    /** 关键词 */
    query?: string;
    remark?: string;
  };

  type CreatorEmailVo = {
    creatorId?: number;
    email?: string;
  };

  type CreatorMediaTrendDailyItem = {
    /** 频率数 */
    commentCnt?: number;
    day?: string;
    /** 点赞数 */
    likeCnt?: number;
    /** 视频列表 */
    mediaList?: TkMediaDto[];
    /** 播放量 */
    viewCnt?: number;
    /** 观众数 */
    viewerCnt?: number;
  };

  type CreatorMediaTrendVo = {
    /** 包含数据 */
    hasData?: boolean;
    /** 按天数据 */
    stats?: CreatorMediaTrendDailyItem[];
  };

  type CreatorPromoteTrendDailyItem = {
    buyerCnt?: number;
    currencySymbol?: string;
    day?: string;
    /** 视频列表 */
    mediaList?: TkMediaDto[];
    orderCnt?: number;
    revenue?: number;
  };

  type CreatorPromoteTrendVo = {
    /** 包含数据 */
    hasData?: boolean;
    /** 按天数据 */
    stats?: CreatorPromoteTrendDailyItem[];
  };

  type CreatorPropVo = {
    propName?: string;
    propValue?: string;
  };

  type DiscountsDto = {
    amount?: number;
    createTime?: string;
    discountCode?: string;
    discountRange?:
      | 'ConsumeCredit'
      | 'Ip'
      | 'Recharge'
      | 'RechargeCredit'
      | 'RpaVoucher'
      | 'TkPack';
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    enabled?: boolean;
    goodsId?: number;
    id?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    remarks?: string;
    threshold?: number;
  };

  type DiscountsVo = {
    /** 赠送数量或折扣百分比或阶梯折扣百分比 */
    amount?: number;
    /** 打折code */
    discountCode?: string;
    /** 打折还是赠送 */
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    /** 周期或数量单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 备注 */
    remarks?: string;
    /** 期数或数量 */
    threshold?: number;
  };

  type DistributionCodeDto = {
    amount?: number;
    code?: string;
    createTime?: string;
    description?: string;
    discountId?: number;
    distributionType?: 'Deduction' | 'Discount' | 'Official';
    distributor?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    limited?: boolean;
    name?: string;
    systemDefault?: boolean;
    usageCount?: number;
    usedCount?: number;
    valid?: boolean;
    validDays?: number;
  };

  type DistributionInfo = {
    code?: DistributionCodeDto;
    deductedPrice?: number;
    drawPrice?: number;
  };

  type DocumentDto = {
    category?: string;
    content?: string;
    createTime?: string;
    creatorId?: number;
    id?: number;
    link?: string;
    sortNo?: number;
    status?: 'Deleted' | 'Draft' | 'Released';
    teamId?: number;
    title?: string;
    type?: 'Email';
    updateTime?: string;
    updaterId?: number;
  };

  type ExportTkCreatorRequest = {
    ids?: number[];
    props?: string[];
    scene?:
      | 'General'
      | 'Gifter'
      | 'InsUser'
      | 'LiveCreator'
      | 'ShopBuyer'
      | 'ShopCreator'
      | 'User'
      | 'VideoCreator';
    teamId?: number;
  };

  type FingerprintTemplateDto = {
    browser?: 'Chrome' | 'Edge' | 'Safari';
    configId?: string;
    createTime?: string;
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    creatorId?: number;
    description?: string;
    id?: number;
    /** 如果不为空则说明该模板生成的实例固定使用该版本号，否则使用随机版本 */
    majorVersion?: number;
    maxIns?: number;
    md5sum?: string;
    name?: string;
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    teamId?: number;
    usedIns?: number;
  };

  type GoodsDto = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type HasInteractionRequest = {
    handlers?: string[];
    interactTimeFrom?: number;
    interactTimeTo?: number;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    shopId?: number;
  };

  type HasInteractionVo = {
    count?: number;
    handle?: string;
  };

  type ItemPriceInfo = {
    costPrice?: number;
    /** 当前过期时间 */
    currentValidEndTime?: string;
    discount?: DiscountsVo;
    /** 打折减掉的金额，如果是打折的话 */
    discountAmount?: number;
    goodsId?: number;
    /** 应付价格 */
    payablePrice?: number;
    /** 赠送数量，如果是赠送的话。目前只出现在购买花瓣 */
    presentAmount?: number;
    /** item总价 */
    price?: number;
    /** 续费后到期时间 */
    validEndTime?: string;
  };

  type LadderPriceRange = {
    /** 阶梯折扣百分比（=原价*amount/100） */
    amount?: number;
    /** 超过特定数量 */
    threshold?: number;
  };

  type OpenaiChatGenerateRequest = {
    accessToken?: string;
    aiProvider?: string;
    count?: number;
    lang?: string;
    prompt?: string;
  };

  type OpenaiChatTranslateRequest = {
    accessToken?: string;
    aiProvider?: string;
    langList?: string[];
    prompt?: string;
  };

  type OpenapiAkDto = {
    accessKeyId?: string;
    accessKeySecret?: string;
    akType?: 'Global' | 'Partner' | 'PortalTeamMember';
    createTime?: string;
    creator?: number;
    id?: number;
    status?: 'Deleted' | 'Disabled' | 'Enabled';
    teamId?: number;
    updateTime?: string;
    userId?: number;
  };

  type OrderDetailVo = {
    /** 是否为自动订单，例如自动续费订单 */
    automatic?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 如果是银行卡支付，则包含银行卡信息 */
    bankPayConfig?: BankPayConfig;
    cashPayType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    createTime?: string;
    creatorId?: number;
    discountReason?: string;
    earnedPartner?: number;
    /** 钱款入账的代理商信息 */
    earnedPartnerDto?: PartnerDto;
    id?: number;
    /** 订单锁定时间;订单锁定之后60分钟不支付会被取消掉 */
    lockTime?: string;
    nickname?: string;
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** 如果父订单不为空说明该订单被选中合并支付了 */
    parentOrderId?: number;
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 订单应付总额，比如说买一年打8折或者销售改价之后的价格 */
    payablePrice?: number;
    /** 充值赠送金额;只在充值订单有效 */
    presentAmount?: number;
    /** 生产备注 */
    productionRemarks?: string;
    /** 生产状态 */
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    /** 实付金额，即现金支付金额;可开票金额以此为依据 */
    realPrice?: number;
    /** 销售改价折现 */
    salesReduction?: number;
    serialNumber?: string;
    /** 订单总额 */
    totalPrice?: number;
    /** 代金券抵扣金额 */
    voucherAmount?: number;
    /** 代金券卡号 */
    voucherCardNumber?: string;
  };

  type OrderItemDto = {
    count?: number;
    discountReason?: string;
    extraInfo?: string;
    goodsId?: number;
    goodsPeriodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    itemPrice?: number;
    orderId?: number;
    productionRemarks?: string;
    productionStatus?:
      | 'Finished'
      | 'NotStart'
      | 'ProduceError'
      | 'Producing'
      | 'ReFunded'
      | 'RefundError'
      | 'Refunding'
      | 'WaitReFund';
    resourceId?: number;
    resourceName?: string;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    teamId?: number;
  };

  type PageCreatorRequest = {
    alias?: string;
    basicInit?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    handle?: string;
    includeDocument?: boolean;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    lastInteractTimeFrom?: string;
    lastInteractTimeTo?: string;
    lastInteractType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    location?: string;
    mediaInit?: boolean;
    pageNum?: number;
    pageSize?: number;
    queryList?: RangeQueryVo[];
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: number[];
  };

  type PageCreatorSimpleRequest = {
    alias?: string;
    basicInit?: boolean;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    followerQueryList?: QueryItemVo[];
    handle?: string;
    includeDocument?: boolean;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    location?: string;
    mediaInit?: boolean;
    pageNum?: number;
    pageSize?: number;
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: number[];
    viewName?: string;
  };

  type PageResultOrderDetailVo = {
    current?: number;
    list?: OrderDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkBuyerVo = {
    current?: number;
    list?: TkBuyerVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkCreatorDetailVo = {
    current?: number;
    list?: TkCreatorDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkCreatorDto = {
    current?: number;
    list?: TkCreatorDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkInteractionDetailVo = {
    current?: number;
    list?: TkInteractionDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkProductDto = {
    current?: number;
    list?: TkProductDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTkSearchTaskVo = {
    current?: number;
    list?: TkSearchTaskVo[];
    pageSize?: number;
    total?: number;
  };

  type PageTkCreatorRequest = {
    alias?: string;
    filterByFavorite?: boolean;
    followerCntFrom?: number;
    followerCntTo?: number;
    followerQueryList?: QueryItemVo[];
    handle?: string;
    includeDocument?: boolean;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    lastInteractTimeFrom?: string;
    lastInteractTimeTo?: string;
    lastInteractType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    location?: string;
    pageNum?: number;
    pageSize?: number;
    queryList?: RangeQueryVo[];
    shopId?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: number[];
  };

  type PartnerDto = {
    bankAccount?: string;
    bankName?: string;
    bankNo?: string;
    contactName?: string;
    contactPhone?: string;
    createTime?: string;
    fullName?: string;
    id?: number;
    managerId?: number;
    oemSupport?: boolean;
    openapiSupport?: boolean;
    organizedTeamAccountQuota?: number;
    organizedTeamUserQuota?: number;
    password?: string;
    role?: 'Broker' | 'Organizer';
    shortName?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamId?: number;
    userId?: number;
  };

  type QueryItemVo = {
    key?: string;
    value?: number;
  };

  type RangeQueryVo = {
    from?: number;
    key?: string;
    to?: number;
  };

  type Resource = true;

  type RpaFlowDto = {
    attachmentSize?: number;
    bizCode?: string;
    configId?: string;
    console?: boolean;
    createTime?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    creatorId?: number;
    description?: string;
    dirty?: boolean;
    execCount?: number;
    expireTime?: string;
    flowShareCode?: string;
    groupId?: number;
    id?: number;
    lastExecTime?: string;
    marketId?: number;
    name?: string;
    nameBrief?: string;
    numberVersion?: number;
    packId?: number;
    publishTime?: string;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    sessionInner?: boolean;
    sharedFlowId?: number;
    shopId?: number;
    sortNo?: number;
    status?: 'Draft' | 'Published';
    supportConcurrent?: boolean;
    teamId?: number;
    tkFlowId?: number;
    updateTime?: string;
    version?: string;
  };

  type RpaFlowGroupVo = {
    description?: string;
    id?: number;
    name?: string;
    sortNumber?: number;
    teamId?: number;
  };

  type RpaFlowVo = {
    allowPushUpdate?: boolean;
    /** 是否可读。针对分享流程和市场流程 */
    allowRead?: boolean;
    bizCode?: string;
    configId?: string;
    console?: boolean;
    createTime?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    creatorId?: number;
    description?: string;
    dirty?: boolean;
    /** 过期时间，仅针对引用市场流程 */
    expireTime?: string;
    /** 是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的 */
    expired?: boolean;
    extra?: Record<string, any>;
    flowShareCode?: string;
    groups?: RpaFlowGroupVo[];
    id?: number;
    /** 对应的市场模板ID */
    marketId?: number;
    /** 如果是市场流程，显示市场流程的最新版本 */
    marketLatestVersion?: string;
    name?: string;
    nameBrief?: string;
    /** 数字版本号，会从1开始累加 */
    numberVersion?: number;
    platforms?: RpaPlatformVo[];
    publishTime?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    sessionInner?: boolean;
    shareFromTeamId?: number;
    shareFromTeamName?: string;
    /** 如果是分享过来的流程，显示被分享的流程最新的版本 */
    shareLatestVersion?: string;
    /** 不为空表示是他人分享的流程 */
    sharedFlowId?: number;
    shopId?: number;
    sortNo?: number;
    status?: 'Draft' | 'Published';
    supportConcurrent?: boolean;
    /** 团队ID; */
    teamId?: number;
    teamName?: string;
    tkFlowId?: number;
    updateTime?: string;
    version?: string;
  };

  type RpaPlatformVo = {
    flowId?: number;
    platformName?: string;
  };

  type RpaTaskDto = {
    clientId?: string;
    clientIp?: string;
    cloudInstanceId?: number;
    concurrent?: number;
    configId?: string;
    console?: boolean;
    createTime?: string;
    creatorId?: number;
    creditDetailId?: number;
    description?: string;
    deviceName?: string;
    errorCode?: number;
    errorMsg?: string;
    executeEndTime?: string;
    executeTime?: string;
    extra?: string;
    fileLocked?: boolean;
    fileStatus?: 'Deleted' | 'Undefined' | 'Valid';
    flowId?: number;
    flowName?: string;
    forceRecord?: boolean;
    heartbeatTime?: string;
    hyRuntimeId?: number;
    id?: number;
    manualRun?: boolean;
    name?: string;
    planId?: number;
    planName?: string;
    preview?: boolean;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    region?: string;
    rpaVoucherId?: number;
    runOnCloud?: boolean;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    teamId?: number;
  };

  type ShopChatVo = {
    /** 沟通账号 */
    chatAccount?: string;
    /** 沟通账号类型 */
    chatType?:
      | 'None'
      | 'email'
      | 'facebookMessager'
      | 'line'
      | 'qq'
      | 'skype'
      | 'wechat'
      | 'whatsapp'
      | 'zalo';
  };

  type ShopDto = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopWithPlatformVo = {
    id?: number;
    name?: string;
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    platformArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformId?: number;
    platformName?: string;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    securityPolicyEnabled?: boolean;
    teamId?: number;
    type?: 'Global' | 'Local' | 'None';
  };

  type SyncBuyerBean = {
    /** 用户的头像地址 */
    avatar?: string;
    cid?: string;
    handle?: string;
  };

  type SyncItemBean = {
    productName?: string;
    productNo?: string;
    /** 产品图片 */
    productThumbUrl?: string;
    /** 买了几个 */
    quantity?: number;
    /** 产品单价 */
    unitPrice?: number;
  };

  type SyncOrderBean = {
    /** 该订单付款总额 */
    amount?: number;
    /** 给买家发消息的链接 */
    contactUrl?: string;
    /** 这个订单的创建时间 */
    createTime?: string;
    /** 使用的是哪种货币，What your buyer paid那里可以看到 */
    currency?: string;
    /** 对应订单页面的Location字段 */
    location?: string;
    orderNo?: string;
    phone?: string;
    shipAddress?: string;
  };

  type SyncOrderRequest = {
    buyer?: SyncBuyerBean;
    items?: SyncItemBean[];
    order?: SyncOrderBean;
    shopId?: number;
  };

  type SyncProductRequest = {
    /** 产品列表 */
    products?: TkProductDocument[];
    /** 分身ID */
    shopId?: number;
  };

  type TagDto = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type TagVo = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    /** 管理资源数 */
    resourceCount?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type TaskDto = {
    createTime?: string;
    creatorId?: number;
    detail?: string;
    finishTime?: string;
    id?: number;
    name?: string;
    progress?: number;
    remarks?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    targetId?: number;
    taskType?:
      | 'BatchUpdateBandwidth'
      | 'CleanColdTable'
      | 'CleanMongo'
      | 'CleanTable'
      | 'CopyTkCreatorToClient'
      | 'CrsTransferOrder'
      | 'DbTransfer'
      | 'DeleteIps'
      | 'FireOpsMessage'
      | 'ImportAccounts'
      | 'ImportIp'
      | 'ImportIppIps'
      | 'ImportIps'
      | 'ImportShop'
      | 'OpenaiChatGenerate'
      | 'OpenaiChatTranslate'
      | 'ProbeBatchLaunchInstance'
      | 'ProbeIps'
      | 'RebootIp'
      | 'RefreshClash'
      | 'RefreshExtensions'
      | 'RepairGhLiveTime'
      | 'RepairKolLiveRate'
      | 'RepairKolLiveTime'
      | 'RepairOps'
      | 'RepairTkCreatorFollower'
      | 'ResetJdEip'
      | 'ReviseIpHostLocation'
      | 'ShardTableOps'
      | 'ShardTeamTableSql'
      | 'SshChangePort'
      | 'SshCommands'
      | 'SshCommandsBatchLaunchInstance'
      | 'SyncKolCreator'
      | 'SyncKolRegionMap'
      | 'TkSendEmail'
      | 'TransferShardTable'
      | 'TransferTable'
      | 'TransferTagResource'
      | 'TransferTkCreator'
      | 'UpgradeGhMessage'
      | 'UploadAiKnowledge'
      | 'UploadDiskFile'
      | 'UserRefreshIp';
    teamId?: number;
  };

  type TeamDto = {
    avatar?: string;
    createTime?: string;
    creatorId?: number;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    name?: string;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    tenantId?: number;
    testing?: boolean;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TeamQuotaVo = {
    /** 团队免费配额，null表示使用官方默认，-1表示无限 */
    freeQuota?: number;
    /** 阶梯价格 */
    ladderPrices?: LadderPriceRange[];
    /** 官方单价（花瓣/天），0表示免费使用 */
    price?: number;
    /** （最大）配额(-1表示不限制） */
    quota?: number;
    /** 配额描述 */
    quotaDesc?: string;
    /** 配额名称 */
    quotaName?:
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota';
  };

  type TkAuthorizeUrlVo = {
    nickname?: string;
    shopId?: number;
    shopName?: string;
    teamId?: number;
    teamName?: string;
    tkShops?: TkOpenShopDto[];
    userId?: number;
  };

  type tkBuyerCreatorPageGetParams = {
    /** 根据买家id或者handle精确查找 */
    search?: string;
    /** 店铺ID */
    shopId?: number;
    /** 按标签id过滤 */
    tagIds?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], last_buy_time,handle,order_count,order_amount */
    orderBy?: string;
  };

  type tkBuyerFindBuyerIdsByHandlesGetParams = {
    /** shopId */
    shopId: number;
    /** handles */
    handles: string;
  };

  type tkBuyerFindBuyerOrdersGetParams = {
    /** buyerId */
    buyerId: number;
  };

  type tkBuyerFindLatestOrderNoGetParams = {
    /** shopId */
    shopId: number;
  };

  type TkBuyerVo = {
    avatar?: string;
    cid?: string;
    contactUrl?: string;
    createTime?: string;
    email?: string;
    fb?: string;
    handle?: string;
    id?: number;
    /** 最后购买时间 */
    lastBuyTime?: string;
    line?: string;
    /** 订单总额 */
    orderAmount?: number;
    /** 总订单数 */
    orderCount?: number;
    phone?: string;
    teamId?: number;
    viber?: string;
    whatsapp?: string;
    zalo?: string;
  };

  type tkCommunicationPageGetParams = {
    /** 花漾达人库ID，不是TK库的ID */
    creatorId: number;
    /** 交互时间开始 */
    interactTimeFrom?: string;
    /** 交互时间截止 */
    interactTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type TkCopyToClientRequest = {
    areaCode?: string;
    /** TK客户团队ID */
    clientTeamId?: number;
    /** TK客户团队联系人手机号 */
    clientTeamPhone?: string;
    /** 高级查询条件 */
    complexRequest?: PageCreatorRequest;
    /** 达人总数 */
    count?: number;
    /** 当前页面选择的达人 */
    creatorIds?: number[];
    /** 简易查询条件 */
    simpleRequest?: PageCreatorSimpleRequest;
  };

  type tkCreatorAvatarByHandleGetParams = {
    /** 花漾达人ID */
    handle: string;
  };

  type tkCreatorByCreatorIdGetParams = {
    /** 花漾达人ID */
    creatorId: number;
  };

  type tkCreatorByCreatorIdPropsGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type tkCreatorByCreatorIdPropsPutParams = {
    /** creatorId */
    creatorId: number;
  };

  type tkCreatorByHandleGetParams = {
    /** 花漾达人ID */
    handle: string;
  };

  type TkCreatorDetailVo = {
    alias?: string;
    avatar?: string;
    basicInit?: boolean;
    bio?: string;
    configId?: string;
    creatorId?: string;
    currencySymbol?: string;
    document?: TkCreatorDocument;
    email?: string;
    firstInteractTime?: string;
    /** 首次交互 */
    firstInteraction?: TkInteractionDto;
    followerCnt?: number;
    gmv?: number;
    gmvPerBuyer?: number;
    gpm?: number;
    handle?: string;
    id?: number;
    lastInteractShop?: number;
    lastInteractTime?: string;
    lastInteractType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    /** 最后一次交互 */
    lastInteraction?: TkInteractionDto;
    lastMediaTime?: string;
    lastSyncTime?: string;
    liveGpmMax?: number;
    liveGpmMin?: number;
    location?: string;
    mediaInit?: boolean;
    productCnt?: number;
    /** 自定义字段 */
    propsMap?: Record<string, any>;
    /** 交互的店铺 */
    shops?: ShopDto[];
    statInit?: boolean;
    /** 统计数据 */
    statsMap?: Record<string, any>;
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    unitsSold?: number;
    videoGpmMax?: number;
    videoGpmMin?: number;
  };

  type TkCreatorDocument = {
    /** 联系方式 */
    contactInfo?: Record<string, any>[];
    /** 达人基本信息 */
    creatorInfo?: Record<string, any>;
    /** id */
    id?: string;
    /** 达人统计信息 */
    statsMap?: Record<string, any>;
    /** 热门视频 */
    video?: Record<string, any>[];
  };

  type TkCreatorDocumentSyncRequest = {
    /** 头像数据base64编码 */
    avatarImageBase64?: string;
    /** 头像扩展名 */
    avatarImageExt?: string;
    /** 联系方式 */
    contactInfo?: Record<string, any>[];
    /** 达人基本信息 */
    creatorInfo?: Record<string, any>;
    /** id */
    id?: string;
    /** 达人统计信息 */
    statsMap?: Record<string, any>;
    /** 热门视频 */
    video?: Record<string, any>[];
  };

  type TkCreatorDocumentV2 = {
    /** 头像数据base64编码 */
    avatarImageBase64?: string;
    /** 头像扩展名 */
    avatarImageExt?: string;
    creator_id?: string;
    /** CID */
    creator_oec_id?: string;
    /** 区域 */
    location?: string;
    nick_name?: string;
    /** handle */
    user_name?: string;
  };

  type TkCreatorDto = {
    alias?: string;
    avatar?: string;
    basicInit?: boolean;
    bio?: string;
    configId?: string;
    creatorId?: string;
    currencySymbol?: string;
    email?: string;
    firstInteractTime?: string;
    followerCnt?: number;
    gmv?: number;
    gmvPerBuyer?: number;
    gpm?: number;
    handle?: string;
    id?: number;
    lastInteractShop?: number;
    lastInteractTime?: string;
    lastInteractType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    lastMediaTime?: string;
    lastSyncTime?: string;
    liveGpmMax?: number;
    liveGpmMin?: number;
    location?: string;
    mediaInit?: boolean;
    productCnt?: number;
    statInit?: boolean;
    teamId?: number;
    unitsSold?: number;
    videoGpmMax?: number;
    videoGpmMin?: number;
  };

  type tkCreatorExistsGetParams = {
    /** handle */
    handle: string;
  };

  type TkCreatorFiledVo = {
    /** 字段描述（可能为null） */
    desc?: string;
    filedType?:
      | 'CreatorColumn'
      | 'CreatorContact'
      | 'CreatorProp'
      | 'CreatorStat'
      | 'ProductColumn';
    /** 方案必须包含 */
    force?: boolean;
    /** 字段Key */
    key?: string;
    /** 字段名称 */
    label?: string;
    path?: string;
  };

  type tkCreatorInitInfoPutParams = {
    /** handle */
    handle: string;
    /** basicInit */
    basicInit?: boolean;
    /** mediaInit */
    mediaInit?: boolean;
    /** 最后同步时间，格式：'yyyy-MM-dd HH:mm:ss'，不设置是就是现在 */
    lastSyncTime?: string;
  };

  type tkCreatorListGetParams = {
    /** 精确匹配location */
    location?: string;
    /** mediaInit */
    mediaInit?: boolean;
    /** basicInit */
    basicInit?: boolean;
    /** handle */
    handle?: string;
    /** lastSyncTimeFrom */
    lastSyncTimeFrom?: string;
    /** lastSyncTimeTo */
    lastSyncTimeTo?: string;
    /** lastMediaTimeFrom */
    lastMediaTimeFrom?: string;
    /** lastMediaTimeTo */
    lastMediaTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortFiled */
    sortFiled?: string;
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type tkCreatorPageGetParams = {
    /** 精确匹配creator_id */
    creatorId?: string;
    /** LIKE匹配handle */
    handle?: string;
    /** LIKE匹配alias */
    alias?: string;
    /** LIKE批bio */
    bio?: string;
    /** 精确匹配location */
    location?: string;
    /** 按标签id过滤，当前团队或数据团队的标签均支持 */
    tagIds?: string;
    /** 按当前用户的收藏过滤 */
    filterByFavorite?: boolean;
    /** 交互类型 */
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    /** 店铺ID */
    shopId?: number;
    /** 是否加载document */
    includeDocument?: boolean;
    /** lastSyncTimeFrom */
    lastSyncTimeFrom?: string;
    /** lastSyncTimeTo */
    lastSyncTimeTo?: string;
    /** lastInteractTimeFrom */
    lastInteractTimeFrom?: string;
    /** lastInteractTimeTo */
    lastInteractTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortFiled */
    sortFiled?: string;
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type TkCreatorPropDto = {
    creatorId?: number;
    id?: number;
    propName?: string;
    propValue?: string;
    teamId?: number;
  };

  type tkCreatorViewDownloadByTokenGetParams = {
    /** token */
    token: string;
  };

  type tkCreatorViewPageSimpleGetParams = {
    /** alias */
    alias?: string;
    /** handle */
    handle?: string;
    /** 按标签id过滤 */
    tagIds?: string;
    /** 交互类型 */
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    /** 店铺ID */
    shopId?: number;
    /** 是否加载document */
    includeDocument?: boolean;
    /** 显示方案名称 */
    viewName: string;
    /** 按当前用户的收藏过滤 */
    filterByFavorite?: boolean;
    /** 精确匹配location */
    location?: string;
    /** mediaInit */
    mediaInit?: boolean;
    /** basicInit */
    basicInit?: boolean;
    /** followerCntFrom */
    followerCntFrom?: number;
    /** followerCntTo */
    followerCntTo?: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortFiled */
    sortFiled?: string;
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type tkDiscountInfoGetParams = {
    /** 购买月份 */
    count: number;
    /** packId */
    packId: number;
  };

  type tkEmailByIdDeleteParams = {
    /** id */
    id: number;
  };

  type tkEmailByIdPutParams = {
    /** id */
    id: number;
    /** skipTest */
    skipTest?: boolean;
  };

  type TkEmailDto = {
    encryption?: 'PLAINTEXT' | 'SSL' | 'STARTTLS';
    host?: string;
    id?: number;
    lastSendTime?: string;
    maxPerDay?: number;
    maxReceivers?: number;
    password?: string;
    port?: number;
    sendInterval?: number;
    senderName?: string;
    teamId?: number;
    username?: string;
    workingRange?: string;
  };

  type tkEmailJobByIdGetParams = {
    /** id */
    id: number;
  };

  type tkEmailJobByIdResultPutParams = {
    /** id */
    id: number;
    /** status */
    status: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    /** description */
    description?: string;
  };

  type TkEmailJobVo = {
    creator?: TkCreatorDto;
    document?: DocumentDto;
    email?: TkEmailDto;
    id?: number;
    targetEmail?: string;
  };

  type tkEmailPostParams = {
    /** skipTest */
    skipTest?: boolean;
  };

  type TkFlowDetailVo = {
    bizCode?: string;
    configId?: string;
    createTime?: string;
    description?: string;
    draftConfigId?: string;
    flowGroupName?: string;
    groupId?: number;
    id?: number;
    lastPublishTime?: string;
    lastVersion?: string;
    name?: string;
    nameBrief?: string;
    platformType?: string;
    /** 如果当前套餐包含此流程，则返回购买后的RPA流程 */
    rpaFlow?: RpaFlowDto;
    sessionInner?: boolean;
    sortNo?: number;
    status?: 'Disabled' | 'Enabled' | 'Offline';
    subSystemType?: 'TK';
    supportConcurrent?: boolean;
    /** TK流程的TK分組 */
    tkGroup?: TkFlowGroupDto;
    uiShow?: boolean;
  };

  type TkFlowGroupDto = {
    createTime?: string;
    id?: number;
    name?: string;
    sortNo?: number;
    subSystemType?: 'TK';
  };

  type TkFlowGroupVo = {
    createTime?: string;
    flows?: TkFlowVo[];
    id?: number;
    name?: string;
    sortNo?: number;
    subSystemType?: 'TK';
  };

  type tkFlowsGetParams = {
    /** 购买的套餐ID */
    packId: number;
    /** orderBy */
    orderBy?: string;
  };

  type TkFlowVo = {
    bizCode?: string;
    configId?: string;
    createTime?: string;
    description?: string;
    draftConfigId?: string;
    flowGroupName?: string;
    groupId?: number;
    id?: number;
    lastPublishTime?: string;
    lastVersion?: string;
    name?: string;
    nameBrief?: string;
    packFlows?: TkPackFlowDto[];
    platformType?: string;
    sessionInner?: boolean;
    sortNo?: number;
    status?: 'Disabled' | 'Enabled' | 'Offline';
    subSystemType?: 'TK';
    supportConcurrent?: boolean;
    uiShow?: boolean;
  };

  type tkGroupsBySubSystemTypeGetParams = {
    /** subSystemType */
    subSystemType: 'TK';
  };

  type tkHasInteractionGetParams = {
    /** 店铺ID，为null时，从团队全局统计 */
    shopId?: number;
    /** 达人账号 */
    handle: string;
    /** 交互类型 */
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    /** interactTimeFrom */
    interactTimeFrom?: number;
    /** interactTimeTo */
    interactTimeTo?: number;
  };

  type tkInteractionByIdGetParams = {
    /** id */
    id: number;
  };

  type TkInteractionDetailVo = {
    creatorId?: number;
    description?: string;
    extraInfo?: Record<string, any>;
    flowId?: number;
    id?: number;
    interactTime?: number;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    interactionId?: string;
    operator?: UserDto;
    operatorId?: number;
    rpaFlow?: RpaFlowDto;
    rpaTask?: RpaTaskDto;
    shop?: ShopDto;
    shopId?: number;
    success?: boolean;
    taskId?: number;
    teamId?: number;
  };

  type TkInteractionDto = {
    creatorId?: number;
    description?: string;
    flowId?: number;
    id?: number;
    interactTime?: number;
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    interactionId?: string;
    operatorId?: number;
    shopId?: number;
    success?: boolean;
    taskId?: number;
    teamId?: number;
  };

  type tkInteractionPageGetParams = {
    /** shopId */
    shopId?: number;
    /** 花漾达人库ID，不是TK库的ID */
    creatorId: number;
    /** 交互类型 */
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    /** 操作者ID */
    operatorId?: number;
    /** 交互时间开始 */
    interactTimeFrom?: string;
    /** 交互时间截止 */
    interactTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type TkInteractionVo = {
    /** 达人数据库ID */
    creatorDbId?: number;
    /** 达人ID */
    creatorId?: string;
    /** 交互的描述 */
    description?: string;
    /** 额外信息，转化成JSON总长度不要超过8000 */
    extraInfo?: Record<string, any>;
    /** 操作流程 */
    flowId?: number;
    /** 达人账号 */
    handle?: string;
    /** 交互时间戳 */
    interactTimestamp?: number;
    /** 交互类型 */
    interactType?:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
    /** 交互D */
    interactionId?: string;
    /** 店铺ID */
    shopId?: number;
    /** 是否成功 */
    success?: boolean;
    /** 流程任务ID */
    taskId?: number;
  };

  type tkIsvTokenPostParams = {
    /** 签名使用的AK */
    accessKeyId: string;
    /** 超时时间，单位秒，取值范围：[120,7200] */
    expireSeconds: number;
  };

  type tkLatestInteractionGetParams = {
    /** 店铺ID，为null时，从团队全局统计 */
    shopId?: number;
    /** 达人账号 */
    handle: string;
    /** 交互类型 */
    interactType:
      | 'Chat'
      | 'Email'
      | 'FetchCreator'
      | 'ImChat'
      | 'ImportCreator'
      | 'Ranking'
      | 'SampleRequest'
      | 'SampleRequestApproved'
      | 'TargetPlan'
      | 'UpdateCreator';
  };

  type tkLivestreamDailyTrendGetParams = {
    /** creatorId */
    creatorId: number;
    /** days */
    days?: number;
  };

  type TkMediaDto = {
    buyerCnt?: number;
    coRate?: number;
    commentCnt?: number;
    creatorId?: number;
    currencySymbol?: string;
    duration?: number;
    id?: number;
    likeCnt?: number;
    mediaId?: string;
    mediaType?: 'livestream' | 'video';
    name?: string;
    orderCnt?: number;
    postUrl?: string;
    productImpressions?: number;
    releaseTime?: string;
    revenue?: number;
    shopId?: number;
    teamId?: number;
    viewCnt?: number;
    viewerCnt?: number;
  };

  type tkMediaSyncMediaFromDocumentGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type tkMediaTriggerCalcDailyGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type tkMediaTriggerCalcHomeGetParams = {
    /** creatorId */
    creatorId: number;
  };

  type tkOpenAuthorizeCallbackByServiceIdGetParams = {
    /** service_id */
    service_id: string;
    /** code */
    code: string;
    /** state */
    state: string;
  };

  type tkOpenAuthorizeResultByStateGetParams = {
    /** state */
    state: string;
  };

  type tkOpenAuthorizeUrlGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkOpenBindTkShopGetParams = {
    /** shopId */
    shopId: number;
  };

  type tkOpenNotificationByServiceIdPostParams = {
    /** service_id */
    service_id: string;
  };

  type tkOpenProductRemarkPutParams = {
    /** shopId */
    shopId: number;
    /** productId */
    productId: string;
    /** remark */
    remark: string;
  };

  type tkOpenProductSearchPostParams = {
    /** shopId */
    shopId: number;
    /** includeThumbUrl */
    includeThumbUrl?: boolean;
  };

  type TkOpenShopDto = {
    appId?: number;
    id?: number;
    importTime?: string;
    openUserId?: number;
    shopCipher?: string;
    shopId?: number;
    teamId?: number;
    tkRegion?: string;
    tkShopId?: string;
    tkShopName?: string;
    tkShopType?: number;
  };

  type TkOpenShopVo = {
    appId?: number;
    id?: number;
    importTime?: string;
    openUser?: TkOpenUserDto;
    openUserId?: number;
    shopCipher?: string;
    shopId?: number;
    teamId?: number;
    tkRegion?: string;
    tkShopId?: string;
    tkShopName?: string;
    tkShopType?: number;
  };

  type TkOpenUserDto = {
    accessToken?: string;
    accessTokenExpireIn?: string;
    appId?: number;
    authorized?: boolean;
    createTime?: string;
    deauthorizedTime?: string;
    id?: number;
    lastRefreshTime?: string;
    message?: string;
    openId?: string;
    refreshToken?: string;
    refreshTokenExpireIn?: string;
    sellerBaseRegion?: string;
    sellerName?: string;
    teamId?: number;
    tkUserType?: 'Creator' | 'None' | 'Seller';
    userId?: number;
  };

  type tkOpenUserRefreshTokenGetParams = {
    /** tkUserId */
    tkUserId: number;
  };

  type TkOrderItemVo = {
    id?: number;
    orderId?: number;
    product?: TkProductVo;
    productId?: number;
    productNo?: string;
    quantity?: number;
    teamId?: number;
    unitPrice?: number;
  };

  type tkOrdersGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** id */
    id?: number;
    /** serialNumber */
    serialNumber?: string;
    /** orderType */
    orderType?:
      | 'BuyGiftCard'
      | 'BuyIp'
      | 'BuyPluginPack'
      | 'BuyRpaVoucher'
      | 'BuyTkPack'
      | 'BuyTkshop'
      | 'BuyTraffic'
      | 'CashOut'
      | 'MakeupPriceDifference'
      | 'Merge'
      | 'OrderCancel'
      | 'PartnerBuyVoucher'
      | 'PartnerDraw'
      | 'PartnerRecharge'
      | 'PartnerRechargeCredit'
      | 'PartnerRenewRpaVoucher'
      | 'Present'
      | 'Recharge'
      | 'RechargeCredit'
      | 'Refund'
      | 'RenewIp'
      | 'RenewPluginPack'
      | 'RenewRpaVoucher'
      | 'RenewTkPack'
      | 'RenewTkshop'
      | 'UpgradeTkshop';
    /** payStatus */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** from */
    from?: string;
    /** to */
    to?: string;
  };

  type TkOrderVo = {
    /** 该订单总金额 */
    amount?: number;
    buyerHandle?: string;
    buyerId?: number;
    contactUrl?: string;
    createTime?: string;
    currency?: string;
    id?: number;
    items?: TkOrderItemVo[];
    lasSyncTime?: string;
    /** 对应订单页面的Location属性 */
    location?: string;
    orderNo?: string;
    phone?: string;
    shipAddress?: string;
    shop?: ShopWithPlatformVo;
    shopId?: number;
    teamId?: number;
  };

  type TkPackFlowDto = {
    flowId?: number;
    id?: number;
    packId?: number;
    status?: 'Disabled' | 'Enabled' | 'Offline';
  };

  type TkPackParamDto = {
    id?: number;
    packId?: number;
    paramKey?: string;
    paramValue?: number;
  };

  type TkPackParamVo = {
    label?: string;
    paramKey?: string;
  };

  type tkPacksBySubSystemTypeGetParams = {
    /** subSystemType */
    subSystemType: 'TK';
  };

  type TkPacksWithDistribution = {
    distributionCode?: DistributionCodeDto;
    packs?: TkPackWithDistributionVo[];
  };

  type tkPacksWithDistributionCodeBySubSystemTypeGetParams = {
    /** subSystemType */
    subSystemType: 'TK';
    /** 优惠码 */
    distributionCode: string;
  };

  type TkPackVo = {
    createTime?: string;
    customDevelopment?: boolean;
    /** 对应商品 */
    goods?: GoodsDto;
    id?: number;
    listPrice?: number;
    name?: string;
    /** 套餐参数:paramKey=>value */
    params?: Record<string, any>;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    presentCredit?: number;
    presentIpEnabled?: boolean;
    realPrice?: number;
    shopQuota?: number;
    sortNo?: number;
    subSystemType?: 'TK';
    trailDays?: number;
    trailEnabled?: boolean;
    trailPrice?: number;
    userQuota?: number;
    valid?: boolean;
  };

  type tkPackWithDistributionCodeGetParams = {
    /** 套餐ID */
    packId: number;
    /** 优惠码 */
    distributionCode: string;
  };

  type TkPackWithDistributionVo = {
    /** 拿货价格 */
    costPrice?: number;
    createTime?: string;
    customDevelopment?: boolean;
    /** 扣减掉金额 */
    deductedPrice?: number;
    /** 优惠后价格 */
    distributionPrice?: number;
    /** 对应商品 */
    goods?: GoodsDto;
    id?: number;
    listPrice?: number;
    name?: string;
    /** 套餐参数:paramKey=>value */
    params?: Record<string, any>;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    presentCredit?: number;
    presentIpEnabled?: boolean;
    realPrice?: number;
    shopQuota?: number;
    sortNo?: number;
    subSystemType?: 'TK';
    trailDays?: number;
    trailEnabled?: boolean;
    trailPrice?: number;
    userQuota?: number;
    valid?: boolean;
  };

  type TkParamDto = {
    id?: number;
    maxValue?: number;
    paramKey?: string;
    paramValue?: number;
    teamId?: number;
  };

  type TkParamVo = {
    maxValue?: number;
    paramKey?: string;
    paramLabel?: string;
    paramValue?: number;
  };

  type TkPresentIpVo = {
    goods?: GoodsDto;
    goodsId?: number;
    id?: number;
    platformId?: number;
  };

  type tkProductByIdRemarkPutParams = {
    /** id */
    id: number;
    /** remark */
    remark?: string;
  };

  type TkProductDocument = {
    /** 产品名称 */
    name?: string;
    /** 产品ID */
    no?: string;
    planEnabled?: boolean;
    planRemark?: string;
    /** 单价 */
    price?: number;
    /** 价格单位 */
    priceUnit?: string;
    /** 库存 */
    quantity?: number;
    /** 状态 */
    status?: 'Deactivated' | 'Deleted' | 'Draft' | 'Live' | 'Revieweing' | 'Suspended';
    /** 产品图片 */
    thumbUrl?: string;
    /** 更新时间戳 */
    updateTime?: number;
  };

  type TkProductDto = {
    createTime?: string;
    id?: number;
    lastSyncTime?: string;
    name?: string;
    productNo?: string;
    regions?: string;
    shopId?: number;
    teamId?: number;
    thumbUrl?: string;
    tocRemark?: string;
  };

  type tkProductPageGetParams = {
    itemsSoldFrom?: number;
    itemsSoldTo?: number;
    name?: string;
    pageNum?: number;
    pageSize?: number;
    priceFrom?: number;
    priceTo?: number;
    productNo?: string;
    quantityFrom?: number;
    quantityTo?: number;
    query?: string;
    shopId?: number;
    shopIds?: number[];
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?: 'Deactivated' | 'Deleted' | 'Draft' | 'Live' | 'Revieweing' | 'Suspended';
    updateTimeFrom?: string;
    updateTimeTo?: string;
  };

  type TkProductsSearchRequest = {
    create_time_from?: number;
    create_time_to?: number;
    page_number?: number;
    page_size?: number;
    search_status?: number;
    seller_sku_list?: string;
    update_time_from?: number;
    update_time_to?: number;
  };

  type TkProductsSearchResult = {
    products?: TkProductVo[];
    total?: number;
  };

  type TkProductVo = {
    create_time?: number;
    id?: string;
    name?: string;
    sale_regions?: string[];
    status?: number;
    thumbUrl?: string;
    tocRemark?: string;
    update_time?: number;
  };

  type tkPromoteDailyTrendGetParams = {
    /** creatorId */
    creatorId: number;
    /** days */
    days?: number;
  };

  type TkPropDefDto = {
    defaultValue?: string;
    defineJson?: string;
    id?: number;
    propName?: string;
    propType?: 'list' | 'number' | 'text';
    teamId?: number;
  };

  type tkPropDefineDeleteParams = {
    /** propName */
    propName: string;
  };

  type TkSearchResultDto = {
    createTime?: string;
    creatorAvatar?: string;
    extraInfo?: string;
    handle?: string;
    id?: number;
    imageUrl?: string;
    name?: string;
    releaseTime?: string;
    score?: number;
    taskId?: number;
    teamId?: number;
    videoUrl?: string;
    viewCnt?: number;
  };

  type tkSearchTaskByIdDetailGetParams = {
    /** id */
    id: number;
  };

  type tkSearchTaskByIdStatusPutParams = {
    /** id */
    id: number;
    /** status */
    status: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    /** message */
    message?: string;
  };

  type TkSearchTaskDetailVo = {
    count?: number;
    createTime?: string;
    endTime?: string;
    id?: number;
    imageUrl?: string;
    maxTime?: number;
    minScore?: number;
    minTime?: number;
    platforms?: string;
    query?: string;
    remark?: string;
    resultCount?: number;
    results?: TkSearchResultDto[];
    startTime?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    taskMessage?: string;
    teamId?: number;
  };

  type TkSearchTaskDto = {
    count?: number;
    createTime?: string;
    endTime?: string;
    id?: number;
    imageUrl?: string;
    maxTime?: number;
    minScore?: number;
    minTime?: number;
    platforms?: string;
    query?: string;
    remark?: string;
    startTime?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    taskMessage?: string;
    teamId?: number;
  };

  type tkSearchTaskPageGetParams = {
    pageNum?: number;
    pageSize?: number;
    query?: string;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
  };

  type TkSearchTaskVo = {
    count?: number;
    createTime?: string;
    endTime?: string;
    id?: number;
    imageUrl?: string;
    maxTime?: number;
    minScore?: number;
    minTime?: number;
    platforms?: string;
    query?: string;
    remark?: string;
    resultCount?: number;
    startTime?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    taskMessage?: string;
    teamId?: number;
  };

  type TkShopAddRegionRequest = {
    /** 新增的平台ID */
    platformIds?: number[];
    /** 原始店铺ID */
    shopId?: number;
  };

  type tkShopAreaListByCoordinateIdByCoordinateIdGetParams = {
    /** coordinateId */
    coordinateId: string;
  };

  type tkShopByCoordinateIdByCoordinateIdGetParams = {
    /** coordinateId */
    coordinateId: string;
  };

  type TkShopImportParamVo = {
    /** 是否添加密码记录 */
    addPassword?: boolean;
    /** 是否绑定空闲指纹 */
    bindFingerprint?: boolean;
    /** 是否绑定赠送的IP */
    bindPresentIp?: boolean;
    /** 沟通账户 */
    chats?: ShopChatVo[];
    /** 描述 */
    description?: string;
    /** 额外属性 */
    extraProp?: string;
    /** 绑定IP */
    ipId?: number;
    /** 店铺名称 */
    name?: string;
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    /** 店铺分身密码 */
    password?: string;
    /** 平台ID */
    platformIds?: number[];
    /** 赠送IP 0表示拒绝（不再赠送），null表示未赠送 */
    presentIpId?: number;
    /** 原本的店铺ID，追加区域 */
    shopId?: number;
    /** 是否创建无状态分身 */
    stateless?: boolean;
    /** 关联已有标签 */
    tagIds?: number[];
    /** 创建标签 */
    tags?: string[];
    /** 店铺类型 */
    type?: 'Global' | 'Local' | 'None';
    /** 店铺分身用户名 */
    username?: string;
  };

  type TkSystemStatusVo = {
    /** 自动创建的指纹模版 */
    fingerprintTemplate?: FingerprintTemplateDto;
    /** RPA流程 */
    flows?: RpaFlowVo[];
    /** 购买套餐的订单 */
    order?: OrderDetailVo;
    /** 购买套餐的订单条目 */
    orderItem?: OrderItemDto;
    /** 创建的账号 */
    shops?: ShopDto[];
    /** 创建的团队信息 */
    team?: TeamDto;
    /** 团队配额信息 */
    teamQuotas?: TeamQuotaVo[];
    /** TK团队关系对象 */
    teamRelation?: TkTeamRelationDto;
  };

  type tkTagsGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type TkTeamPackVo = {
    autoRenew?: boolean;
    createTime?: string;
    creatorId?: number;
    customDevelopment?: boolean;
    goodsId?: number;
    id?: number;
    listPrice?: number;
    name?: string;
    packId?: number;
    pauseTime?: string;
    paused?: boolean;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    presentCredit?: number;
    presentIpId?: number;
    quotaMatch?: boolean;
    realPrice?: number;
    renewPrice?: number;
    shopQuota?: number;
    subSystemType?: 'TK';
    teamId?: number;
    userQuota?: number;
    valid?: boolean;
    validEndDate?: string;
  };

  type TkTeamRelationDto = {
    createTime?: string;
    dataTeamId?: number;
    id?: number;
    operationTeamId?: number;
    tkRole?: 'TkClient' | 'TkData' | 'TkOperation';
  };

  type tkTkDowngradePutParams = {
    /** confirm */
    confirm: boolean;
  };

  type tkTkParamPutParams = {
    /** paramKey */
    paramKey: string;
    /** value */
    value: number;
  };

  type tkVideoDailyTrendGetParams = {
    /** creatorId */
    creatorId: number;
    /** days */
    days?: number;
  };

  type UploadGeneralImageRequest = {
    /** 图片的扩展名,默认png，可不设置 */
    ext?: string;
    /** 图片的ID */
    id?: string;
    /** 图片的base64编码 */
    imageBase64?: string;
    /** 是否公共访问 */
    publicAccess?: boolean;
    /** 图片的业务类型 */
    type?: string;
  };

  type UploadMedia2ItemRequest = {
    handle?: string;
    items?: UploadMedia2ItemVo[];
  };

  type UploadMedia2ItemVo = {
    /** 评论数 */
    commentCnt?: number;
    /** 达人ID */
    creatorId?: string;
    /** 时长 */
    duration?: number;
    /** 达人账号 */
    handle?: string;
    /** 点赞数 */
    likeCnt?: number;
    /** 视频或直播ID */
    mediaId?: string;
    /** 媒体类型 */
    mediaType?: 'livestream' | 'video';
    /** 名称 */
    name?: string;
    /** 发布时间 */
    releaseTime?: number;
    /** 观看人数 */
    viewCnt?: number;
    /** 观众数量 */
    viewersCnt?: number;
  };

  type UploadMediaItemRequest = {
    items?: UploadMediaItemVo[];
    shopId?: number;
  };

  type UploadMediaItemVo = {
    buyerCnt?: number;
    coRate?: number;
    /** 评论数 */
    commentCnt?: number;
    /** 达人ID */
    creatorId?: string;
    /** 币种 */
    currencySymbol?: string;
    /** 日期,yyyy-MM-dd */
    day?: string;
    /** 时长 */
    duration?: number;
    /** 达人账号 */
    handle?: string;
    /** 点赞数 */
    likeCnt?: number;
    /** 视频或直播ID */
    mediaId?: string;
    /** 媒体类型 */
    mediaType?: 'livestream' | 'video';
    /** 名称 */
    name?: string;
    paidOrderCnt?: number;
    postUrl?: string;
    productImpressions?: number;
    /** 发布时间 */
    releaseTime?: number;
    revenue?: number;
    viewCnt?: number;
    viewersCnt?: number;
  };

  type UserDto = {
    avatar?: string;
    createTime?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAkAccessToken = {
    code?: number;
    data?: AkAccessToken;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCalcPriceResponse = {
    code?: number;
    data?: CalcPriceResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateBuyTkPackOrderResponse = {
    code?: number;
    data?: CreateBuyTkPackOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateOrderResponse = {
    code?: number;
    data?: CreateOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreatorMediaTrendVo = {
    code?: number;
    data?: CreatorMediaTrendVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreatorPromoteTrendVo = {
    code?: number;
    data?: CreatorPromoteTrendVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDiscountsDto = {
    code?: number;
    data?: DiscountsDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListHasInteractionVo = {
    code?: number;
    data?: HasInteractionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListlong = {
    code?: number;
    data?: number[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListOpenapiAkDto = {
    code?: number;
    data?: OpenapiAkDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopDto = {
    code?: number;
    data?: ShopDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListstring = {
    code?: number;
    data?: (
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam'
    )[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTagVo = {
    code?: number;
    data?: TagVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkCreatorFiledVo = {
    code?: number;
    data?: TkCreatorFiledVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkCreatorPropDto = {
    code?: number;
    data?: TkCreatorPropDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkEmailDto = {
    code?: number;
    data?: TkEmailDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkFlowDetailVo = {
    code?: number;
    data?: TkFlowDetailVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkFlowGroupVo = {
    code?: number;
    data?: TkFlowGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkOrderVo = {
    code?: number;
    data?: TkOrderVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkPackParamDto = {
    code?: number;
    data?: TkPackParamDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkPackParamVo = {
    code?: number;
    data?: TkPackParamVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkPackVo = {
    code?: number;
    data?: TkPackVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkParamVo = {
    code?: number;
    data?: TkParamVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkPresentIpVo = {
    code?: number;
    data?: TkPresentIpVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkPropDefDto = {
    code?: number;
    data?: TkPropDefDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkTeamPackVo = {
    code?: number;
    data?: TkTeamPackVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultOrderDetailVo = {
    code?: number;
    data?: PageResultOrderDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkBuyerVo = {
    code?: number;
    data?: PageResultTkBuyerVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkCreatorDetailVo = {
    code?: number;
    data?: PageResultTkCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkCreatorDto = {
    code?: number;
    data?: PageResultTkCreatorDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkInteractionDetailVo = {
    code?: number;
    data?: PageResultTkInteractionDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkProductDto = {
    code?: number;
    data?: PageResultTkProductDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTkSearchTaskVo = {
    code?: number;
    data?: PageResultTkSearchTaskVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTaskDto = {
    code?: number;
    data?: TaskDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkAuthorizeUrlVo = {
    code?: number;
    data?: TkAuthorizeUrlVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkCreatorDetailVo = {
    code?: number;
    data?: TkCreatorDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkCreatorDto = {
    code?: number;
    data?: TkCreatorDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkEmailDto = {
    code?: number;
    data?: TkEmailDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkEmailJobVo = {
    code?: number;
    data?: TkEmailJobVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkInteractionDetailVo = {
    code?: number;
    data?: TkInteractionDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkOpenShopVo = {
    code?: number;
    data?: TkOpenShopVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkPacksWithDistribution = {
    code?: number;
    data?: TkPacksWithDistribution;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkPackWithDistributionVo = {
    code?: number;
    data?: TkPackWithDistributionVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkParamDto = {
    code?: number;
    data?: TkParamDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkProductsSearchResult = {
    code?: number;
    data?: TkProductsSearchResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkPropDefDto = {
    code?: number;
    data?: TkPropDefDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkSearchResultDto = {
    code?: number;
    data?: TkSearchResultDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkSearchTaskDetailVo = {
    code?: number;
    data?: TkSearchTaskDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkSearchTaskDto = {
    code?: number;
    data?: TkSearchTaskDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTkSystemStatusVo = {
    code?: number;
    data?: TkSystemStatusVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
