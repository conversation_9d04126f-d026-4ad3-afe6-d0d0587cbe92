// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 计算购买Tkshop的价格 POST /api/tkshop/calcBuyTkshop */
export async function tkshopCalcBuyTkshopPost(
  body: API.CreateBuyTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcTkshopPriceResponse>('/api/tkshop/calcBuyTkshop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 计算购买Tkshop的价格 POST /api/tkshop/calcUpgradeTkshop */
export async function tkshopCalcUpgradeTkshopPost(
  body: API.CreateBuyTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcTkshopPriceResponse>('/api/tkshop/calcUpgradeTkshop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建购买Tkshop订单 POST /api/tkshop/createBuyTkshop */
export async function tkshopCreateBuyTkshopPost(
  body: API.CreateBuyTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateBuyTkPackOrderResponse>('/api/tkshop/createBuyTkshop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据试用码创建套餐 GET /api/tkshop/createByTrialCode */
export async function tkshopCreateByTrialCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreateByTrialCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamDto>('/api/tkshop/createByTrialCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建续费Tkshop订单 POST /api/tkshop/createRenewTkshop */
export async function tkshopCreateRenewTkshopPost(
  body: API.CreateBuyTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/tkshop/createRenewTkshop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建升级Tkshop订单 POST /api/tkshop/createUpgradeTkshop */
export async function tkshopCreateUpgradeTkshopPost(
  body: API.CreateBuyTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/tkshop/createUpgradeTkshop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将当前Tkshop团队降级为普通团队 PUT /api/tkshop/downgrade */
export async function tkshopDowngradePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopDowngradePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/downgrade', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取子系统配置 GET /api/tkshop/systemConfig */
export async function tkshopSystemConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTkshopConfig>('/api/tkshop/systemConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询当前团队状态 GET /api/tkshop/systemStatus */
export async function tkshopSystemStatusGet(options?: { [key: string]: any }) {
  return request<API.WebResultTkshopSystemStatusVo>('/api/tkshop/systemStatus', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询试用码 GET /api/tkshop/trialCode */
export async function tkshopTrialCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopTrialCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopTrialCodeDto>('/api/tkshop/trialCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 开通/关闭达人分配功能 PUT /api/tkshop/updateAllocateCreatorEnabled */
export async function tkshopUpdateAllocateCreatorEnabledPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopUpdateAllocateCreatorEnabledPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/updateAllocateCreatorEnabled', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
