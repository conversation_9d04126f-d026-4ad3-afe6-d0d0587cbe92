import I18N from '@/i18n';
import { getJwt } from '@/utils/utils';
import DMConfirm, { DMLoading } from '@/components/Common/DMConfirm';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import pMinDelay from 'p-min-delay';

const portQueue = [47326, 47327, 47328, 47329, 47330];

// 类型定义
export type NativeEvent = 'checkEmailService' | 'visitShop' | 'openBrowsers';

/**
 * 客户端管理器 - 单例模式
 * 统一管理花漾客户端的连接、状态和批量操作
 */
class ClientManager {
  private static instance: ClientManager;
  private port: number | null = null;
  private isConnecting = false;
  private connectionPromise: Promise<number> | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ClientManager {
    if (!ClientManager.instance) {
      ClientManager.instance = new ClientManager();
    }
    return ClientManager.instance;
  }

  /**
   * 检查客户端是否存活
   */
  private async isClientAlive(port: number): Promise<boolean> {
    try {
      const response = await window.fetch(`http://127.0.0.1:${port}/ping`, {
        credentials: 'omit',
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 检查客户端连接
   */
  private async checkClient(timeout = 2000): Promise<number> {
    return new Promise<number>((resolve, reject) => {
      let targetPort = 0;
      const timer = setTimeout(() => {
        reject(new Error(I18N.t('检测超时')));
      }, timeout);

      const promises = portQueue.map((port) => {
        return new Promise((rsv, rjc) => {
          const t = setTimeout(() => {
            rjc(new Error('timeout'));
          }, 1000);

          window
            .fetch(`http://127.0.0.1:${port}/ping`, {
              credentials: 'omit',
            })
            .then((r) => {
              clearTimeout(t);
              if (!targetPort) {
                targetPort = port;
                clearTimeout(timer);
                resolve(port);
              }
              rsv(r);
            })
            .catch((err) => {
              rjc(err);
            });
        });
      });

      Promise.allSettled(promises).then(() => {
        if (!targetPort) {
          clearTimeout(timer);
          reject(new Error(I18N.t('花漾客户端没有启动/安装')));
        }
      });
    });
  }

  /**
   * 建立客户端连接
   */
  private async connect(): Promise<number> {
    this.isConnecting = true;

    try {
      const port = await this.checkClient();
      this.port = port;
      return port;
    } catch (error) {
      this.showClientNotFoundDialog();
      throw error;
    } finally {
      this.isConnecting = false;
      this.connectionPromise = null;
    }
  }

  /**
   * 确保客户端连接
   */
  async ensureConnection(): Promise<number> {
    // 如果已有有效连接，直接返回
    if (this.port && (await this.isClientAlive(this.port))) {
      return this.port;
    }

    // 如果正在连接，等待连接完成
    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise;
    }

    // 建立新连接
    this.connectionPromise = this.connect();
    return this.connectionPromise;
  }

  /**
   * 检查JWT一致性
   */
  private async checkJwtConsistency(port: number, jwt: string): Promise<void> {
    const res = await window.fetch(`http://127.0.0.1:${port}/checkJwt?jwt=${jwt}`);
    const data = await res.json();

    if (data.hasJwt && !data.isSame) {
      return new Promise<void>((resolve, reject) => {
        DMConfirm({
          iconType: 'info',
          title: I18N.t('花漾客户端当前登录者的身份不一致'),
          content: I18N.t(
            '花漾客户端当前登录者的身份与您的身份不一致，如果确信要切换登录者身份，请点击"重新登录"，请注意，此举会中断旧有登录者当前的工作',
          ),
          okText: I18N.t('重新登录'),
          onOk: async () => {
            await window
              .fetch(`http://127.0.0.1:${port}/setJwt?jwt=${jwt}`)
              .then(() => {
                resolve();
              })
              .catch(reject);
          },
          onCancel() {
            reject(new Error('用户取消操作'));
          },
        });
      });
    }
  }

  /**
   * 执行客户端请求
   */
  private async executeClientRequest(
    url: string,
    method: 'get' | 'post' = 'post',
    body?: any,
  ): Promise<any> {
    const response = await window.fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: method === 'post' ? JSON.stringify(body) : undefined,
    });

    const data = await response.json();

    if (data.success) {
      return data;
    } else {
      throw new Error(data.message || '未知错误');
    }
  }

  /**
   * 显示客户端未找到对话框
   */
  private showClientNotFoundDialog(): void {
    DMConfirm({
      iconType: 'info',
      title: I18N.t('花漾客户端没有启动（或没有安装）'),
      content: I18N.t(
        '花漾客户端没有启动或者没有安装，如果您已经正确安装，请点击"打开花漾客户端"，否则，请下载并重新安装花漾客户端',
      ),
      footerBtns: [
        {
          key: 'openClient',
          label: I18N.t('打开花漾客户端'),
          btnProps: {
            type: 'primary',
          },
          onClick: () => {
            this.openPureClient();
          },
        },
      ],
    });
  }

  /**
   * 简单的打开客户端
   */
  openPureClient(): void {
    const jwt = getJwt();
    location.href = `huayoung://team/${getCurrentTeamId()}/shopManage/all?jwt=${jwt}`;
  }

  /**
   * 执行RPC调用
   */
  async executeRpc(rpaEvent: NativeEvent, payload?: Record<string, any>): Promise<any> {
    // 显示加载提示
    const loadingModal = DMLoading({
      title: I18N.t('正在唤醒花漾客户端，请稍候...'),
      content: I18N.t(
        '系统正在为您唤醒花漾客户端使其按照您的指令行事，唤醒成功后此对话框会自动消失',
      ),
    });

    try {
      const port = await pMinDelay(this.ensureConnection(), 1000);
      // 客户端连接成功后立即关闭loading
      loadingModal.destroy();

      const jwt = getJwt();
      await this.checkJwtConsistency(port, jwt);
      const url = `http://127.0.0.1:${port}/rpc/${rpaEvent}`;
      const body = { jwt, teamId: getCurrentTeamId(), ...(payload || {}) };
      return this.executeClientRequest(url, 'post', body);
    } catch (error) {
      // 发生错误时确保关闭loading
      loadingModal.destroy();
      throw error;
    }
  }

  /**
   * 重置连接状态
   */
  resetConnection(): void {
    this.port = null;
    this.isConnecting = false;
    this.connectionPromise = null;
  }
}

// 导出便捷方法
export default ClientManager.getInstance();
