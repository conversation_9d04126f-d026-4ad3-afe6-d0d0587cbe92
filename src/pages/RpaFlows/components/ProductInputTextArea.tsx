import { Button, Input } from 'antd';
import _ from 'lodash';
import DMFormItem from '@/components/Common/DMFormItem';
import { useTkProductListModal } from '@/pages/ProductManage/components/ProductListModal';
import { useMemo } from 'react';

const ProductInputTextArea = (props: { shop?: API.ShopDetailVo; height?: number }) => {
  const { shop, height = 100 } = props;
  const openProductList = useTkProductListModal();

  const placeholder = useMemo(() => {
    let str = '请输入商品ID，每行一个ID，可为空';
    if (shop) {
      str += '\n' + '也可直接选择';
    }
    return str;
  }, [shop]);
  return (
    <DMFormItem label={'商品列表'} shouldUpdate style={{ marginBottom: 0 }}>
      {(form) => {
        const onChange = (value: string) => {
          form.setFieldValue('productIds', value);
        };
        const value = form.getFieldValue('productIds') || '';
        return (
          <div style={{ display: 'flex', gap: 12, overflow: 'hidden' }}>
            <DMFormItem name={'productIds'} style={{ flex: 1 }}>
              <Input.TextArea
                style={{ height, overflow: 'auto', resize: 'none' }}
                onChange={(e) => {
                  const _value = e.target.value;
                  const validNumber = _value.replace(/[^0-9\n]/g, '');
                  onChange(validNumber);
                }}
                value={value}
                placeholder={placeholder}
              />
            </DMFormItem>
            {shop && (
              <Button
                hidden={!shop}
                type={'primary'}
                onClick={() => {
                  if (shop) {
                    openProductList({
                      shopId: shop.id!,
                      selected: value.split('\n'),
                      onSubmit(ids) {
                        const _value = form.getFieldValue('productIds') || '';
                        let list = _value?.split('\n') || [];
                        list.push(...ids);
                        list = _.uniq(list);
                        list = list.filter(Boolean);
                        onChange(list.join('\n'));
                      },
                    });
                  }
                }}
              >
                选择商品
              </Button>
            )}
          </div>
        );
      }}
    </DMFormItem>
  );
};
export default ProductInputTextArea;
