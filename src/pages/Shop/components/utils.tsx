import moment from 'moment/moment';
import { Form, InputNumber, Space, Tooltip, Typography } from 'antd';
import I18N from '@/i18n';
import { visitShop } from '@/utils/pageUtils';
import IconFontIcon from '@/components/Common/IconFontIcon';
import type { TaskShelfJobType } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { GhostModalCaller } from '@/mixins/modal';
import TriggerFlowConfirmModal from '@/pages/TikTok/components/TriggerFlowConfirmModal';
import {
  tkshopJobsBatchSyncSampleCreatorRequestPost,
  tkshopJobsSendLoginCheckPost,
  tkshopJobsSendSyncShopInfoPost,
  tkshopJobsSendTargetPlanClearPost,
} from '@/services/api-TKShopAPI/TkShopJobController';
import DMFormItem from '@/components/Common/DMFormItem';
import { ShopDetailNode } from '@/components/Common/ShopNode';
import DMConfirm, { DMLoading } from '@/components/Common/DMConfirm';
import _ from 'lodash';
import HelpLink from '@/components/HelpLink';
import { dateFormat } from '@/utils/utils';
import constants from '@/constants';
import pMinDelay from 'p-min-delay';
import { shopDeletePost } from '@/services/api-ShopAPI/ShopController';
import Placeholder from '@/components/Common/Placeholder';
import { getPrimaryChannelType, getPrimaryIp } from '@/pages/ShopManage/utils';

export function getIpIsolationType(
  shop: API.ShopDetailVo,
): 'ip' | 'direct' | 'system' | 'market' | 'proxy' | 'pool' | undefined {
  const { channels, lanProxy } = shop;
  const channel = channels?.find?.((ip) => ip.primary);
  if (channel) {
    if (channel.ipPool) {
      return 'pool';
    }
    return 'ip';
  }
  if (lanProxy?.enabled) {
    switch (lanProxy.networkType!) {
      case 'UseSystem':
        return 'system';
      case 'UseDirect':
        return 'direct';
      case 'UseProxy':
        return 'proxy';
      default:
        return undefined;
    }
  }
  return undefined;
}

export function triggerShopTask(shops: API.ShopDetailVo[], task: keyof typeof TaskShelfJobType) {
  const shop = shops[0];
  const { id } = shop;
  const ids = shops.map((item) => item.id!);
  let shopName = <ShopDetailNode data={shop} />;
  if (shops.length > 1) {
    shopName = I18N.t('{{count}} 个店铺', {
      count: shops.length?.toLocaleString(),
    });
  }
  GhostModalCaller(
    <TriggerFlowConfirmModal
      taskType={task}
      onSubmit={async (options) => {
        const { device, TS_SyncShopInfo } = options;
        if (task === 'TS_SyncShopInfo') {
          // 同步店铺信息
          const advanceSettings = {
            ...TS_SyncShopInfo,
          };
          if (!shop.lastSyncTime && TS_SyncShopInfo.need_sync_order) {
            let sync_order_month = 6;
            await new Promise((resolve, reject) => {
              DMConfirm({
                width: 680,
                icon: false,
                title: (
                  <Form style={{ fontWeight: 'normal' }} layout={'horizontal'}>
                    <DMFormItem
                      labelWidth={70}
                      label={I18N.t('抓取周期')}
                      extra={
                        <div style={{ position: 'relative', paddingLeft: 20 }}>
                          <Typography.Link style={{ position: 'absolute', top: 0, left: 1 }}>
                            <IconFontIcon size={'inherit'} iconName={'info_24'} />
                          </Typography.Link>
                          <div>
                            <Typography.Text
                              type={'secondary'}
                              style={{
                                whiteSpace: 'normal',
                                textAlign: 'justify',
                              }}
                            >
                              {I18N.t(
                                '第一次执行“店铺信息同步”流程会将指定周期的订单全部抓取到私域系统，帮助您更好的分析达人与买家。第一次执行此流程会全量抓取，导致执行时间较长；后续执行则增量抓取，执行时间会大幅缩减。',
                              )}
                            </Typography.Text>
                          </div>
                          <div>
                            <Typography.Text type={'secondary'}>
                              {I18N.t(
                                '请注意：一次抓取订单最多9000笔，请根据订单数量，决定抓取周期。',
                              )}
                            </Typography.Text>
                            <HelpLink href={'/help/tkshop2/sync'} style={{ marginLeft: 20 }} />
                          </div>
                        </div>
                      }
                    >
                      <Space>
                        <InputNumber
                          min={1}
                          defaultValue={sync_order_month}
                          precision={0}
                          max={12}
                          onChange={(val) => {
                            sync_order_month = val || 1;
                          }}
                          onBlur={(e) => {
                            if (!e.target.value) {
                              e.target.value = '1';
                              sync_order_month = 1;
                            }
                          }}
                        />
                        <span>{I18N.t('个月')}</span>
                      </Space>
                    </DMFormItem>
                  </Form>
                ),
                onOk() {
                  advanceSettings.sync_order_month = sync_order_month;
                  tkshopJobsSendSyncShopInfoPost({
                    deviceId: device.deviceId,
                    shopId: id!,
                    advanceSettings,
                  })
                    .then(resolve)
                    .catch(reject);
                },
                onCancel() {
                  reject();
                },
              });
            });
          } else {
            await tkshopJobsSendSyncShopInfoPost({
              deviceId: device.deviceId,
              shopId: id!,
              advanceSettings,
            });
          }
        } else if (task === 'TS_LoginCheck') {
          await tkshopJobsSendLoginCheckPost({
            deviceId: device.deviceId,
            shopId: id!,
          });
        } else if (task === 'TS_SyncSampleCreator') {
          // 同步索样达人
          let query_contents_in_days = options.query_contents_in_days;
          let query_sample_request_creator_in_days = options.query_sample_request_creator_in_days;
          if (!options.need_query_contents_in_days || !query_contents_in_days) {
            query_contents_in_days = 0;
          }
          if (!options.need_query_sample_request_creator || !query_sample_request_creator_in_days) {
            query_sample_request_creator_in_days = 0;
          }
          await tkshopJobsBatchSyncSampleCreatorRequestPost({
            deviceId: device.deviceId,
            shopIds: ids,
            advanceSettings: {
              query_contents_in_days,
              query_sample_request_creator_in_days,
            },
          });
        } else if (task === 'TS_TargetPlanClear') {
          // 清理定向邀约计划
          await tkshopJobsSendTargetPlanClearPost({
            deviceId: device.deviceId,
            shopId: id!,
            advanceSettings: _.omit(options, 'device'),
          });
        }
      }}
      prefix={<DMFormItem label={I18N.t('店铺')}>{shopName}</DMFormItem>}
    />,
    'TriggerFlowConfirmModal',
  );
}

export function getShopStatus(shop: API.ShopDetailVo) {
  const { lastSyncTime, loginStatus, id, frontUrl, platform } = shop;
  const ip_type = getIpIsolationType(shop);
  const gap = 4;
  const sync_hours = moment().diff(lastSyncTime || 0, 'hours');
  if (!ip_type) {
    return (
      <Tooltip
        title={
          <div style={{ display: 'flex', flexDirection: 'column', gap }}>
            <div>{I18N.t('当前店铺需要设置好IP隔离后才能够访问或者执行流程')}</div>
            <a
              onClick={() => {
                visitShop(id!);
              }}
            >
              {I18N.t('设置IP隔离')}
            </a>
          </div>
        }
      >
        <Typography.Text type={'danger'}>
          <Space>
            <IconFontIcon iconName={'Warning-Circle_24'} />
            <span>{I18N.t('未设置IP隔离')}</span>
          </Space>
        </Typography.Text>
      </Tooltip>
    );
  }
  if (loginStatus !== 'Online') {
    return (
      <Tooltip
        title={
          <div style={{ display: 'flex', flexDirection: 'column', gap }}>
            <div>
              {I18N.t('当前店铺未登录或者已掉线，您需要立即完成登录，登录后关闭浏览器即可')}
            </div>
            <div>
              <a
                onClick={() => {
                  visitShop(id!, frontUrl || platform?.loginUrl);
                }}
              >
                {I18N.t('立即登录')}
              </a>
              <a
                style={{ marginLeft: 24 }}
                onClick={() => {
                  triggerShopTask([shop], 'TS_LoginCheck');
                }}
              >
                {I18N.t('重新检查登录状态')}
              </a>
            </div>
          </div>
        }
      >
        <Typography.Text type={'danger'}>
          <Space>
            <IconFontIcon iconName={'Warning-Circle_24'} />
            <span>{I18N.t('未登录或已掉线')}</span>
          </Space>
        </Typography.Text>
      </Tooltip>
    );
  }
  if (sync_hours < 72 && loginStatus === 'Online') {
    return (
      <Tooltip title={I18N.t('店铺状态一切正常')}>
        <Typography.Text type={'success'}>
          <Space>
            <IconFontIcon iconName={'Check-Circle_24'} />
            <span>{I18N.t('正常')}</span>
          </Space>
        </Typography.Text>
      </Tooltip>
    );
  }
  return (
    <Tooltip
      title={
        <div style={{ display: 'flex', flexDirection: 'column', gap }}>
          <div>{I18N.t('您需要立即执行一次“店铺信息同步“流程')}</div>
          <a
            onClick={() => {
              triggerShopTask([shop], 'TS_SyncShopInfo');
            }}
          >
            {I18N.t('店铺信息同步')}
          </a>
        </div>
      }
    >
      <Typography.Text type={'warning'}>
        <Space>
          <IconFontIcon iconName={'Warning-Circle_24'} />
          <span>{I18N.t('等待信息同步')}</span>
        </Space>
      </Typography.Text>
    </Tooltip>
  );
}
export function getShopLastSyncTime(shop: TkShopDetailVo) {
  // 如果更新时间与当前时间小于72小时，就是绿色，否则就是警告色
  const { lastSyncTime } = shop;
  const sync_hours = moment().diff(lastSyncTime || 0, 'hours');
  if (!lastSyncTime) {
    return <Typography.Text type={'warning'}>未更新</Typography.Text>;
  }
  const timeLabel = dateFormat(lastSyncTime, 'MM-DD HH:mm');
  return (
    <Typography.Text type={sync_hours < 72 ? 'success' : 'warning'}>{timeLabel}</Typography.Text>
  );
}
export function getShopExtensionType(shop: TkShopDetailVo) {
  // 如果更新时间与当前时间小于72小时，就是绿色，否则就是警告色
  const { extension } = shop;
  if (extension === 'extension') {
    return '其它浏览器（基于插件）';
  }
  return '花漾浏览器';
}
export function getShopArea(shop: TkShopDetailVo) {
  const { area } = shop?.platform;
  return constants.Area[area] || area;
}
export function getShopType(shop: TkShopDetailVo) {
  const { type } = shop;
  return type === 'Global' ? '跨境店' : '本土店';
}
export type TkShopDetailVo = API.ShopHealthVo;
export function deleteShops(ids: number[], onUpdate: () => any) {
  const confirm = DMConfirm({
    title: I18N.t('确定要删除当前店铺吗？'),
    content: I18N.t('店铺一旦删除，无法恢复，请确认是否继续'),
    async onOk() {
      confirm.destroy();
      const loadingDialog = DMLoading({
        title: '正在为您删除店铺，请稍候...',
      });
      try {
        await pMinDelay(shopDeletePost({ recycled: false }, { ids: ids }), 1000);
        onUpdate();
        loadingDialog.destroy();
      } catch (error) {
        loadingDialog.destroy();
      }
    },
  });
}
const ShopHealthStatusLabel = {
  NotRestricted: 'Good health',
  Restricted: 'Needs improvement',
  ShopClose: 'Shop closed',
};
export function getShopHealthStatus(shop: TkShopDetailVo) {
  const { healthStatus } = shop;
  if (_.isNil(healthStatus)) {
    return <Placeholder>未知</Placeholder>;
  }
  return (
    <Typography.Text
      type={
        healthStatus === 'NotRestricted'
          ? 'success'
          : healthStatus === 'ShopClose'
          ? 'danger'
          : 'warning'
      }
    >
      {ShopHealthStatusLabel[healthStatus]}
    </Typography.Text>
  );
}
export function getShopScore(shop: TkShopDetailVo) {
  const { healthScore } = shop;
  if (_.isNil(healthScore)) {
    return <Placeholder>未知</Placeholder>;
  }
  return (
    <Typography.Text type={healthScore < 4 ? 'warning' : 'success'}>{healthScore}</Typography.Text>
  );
}
export function isIpEmpty(record: TkShopDetailVo) {
  const ipData = getPrimaryIp(record);
  const channelType = getPrimaryChannelType(record);
  if (channelType === 'none' || (channelType === 'ip' && !ipData)) {
    return !record.lanProxy?.enabled;
  }
  return false;
}
export default function openShopBrowser(shop: TkShopDetailVo) {
  if (shop.extension === 'extension') {
    DMConfirm({
      type: 'info',
      title: I18N.t('无法为您打开其它浏览器'),
      content: I18N.t('系统只能为您打开由花漾浏览器运营的店铺'),
      width: 520,
    });
    return;
  }
  if (isIpEmpty(shop)) {
    DMConfirm({
      width: 460,
      type: 'info',
      title: I18N.t('无法打开未设置IP隔离的浏览器分身'),
      content: I18N.t('请在花漾客户端内完成对浏览器分身的IP隔离设置'),
      okText: I18N.t('打开客户端'),
      onOk: () => {
        visitShop(shop.id!, 'DETAIL');
      },
    });
    return;
  }
  visitShop(shop.id!, 'BROWSER');
}
