import { useRef, useState } from 'react';
import { Alert, Button, Checkbox, Form, Input, message, Radio, Space, Typography } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import HelpTooltip from '@/components/HelpTooltip';
import TkRegionSelector from '@/components/Common/Selector/TkRegionSelector';
import { onlineService, openClient, visitShop } from '@/utils/pageUtils';
import { SkipErrorNotifyOption, trimValues } from '@/utils/utils';
import type { UsernameFieldInstance } from '@/pages/Shop/components/UsernameField';
import DMModal from '@/components/Common/Modal/DMModal';
import DMConfirm from '@/components/Common/DMConfirm';
import HelpLink from '@/components/HelpLink';
import { tkShopPost } from '@/services/api-TKAPI/TkShopController';
import { isArray } from 'lodash';
import validate from '@/utils/validate';
import TkAreaConstants from '@/constants/TkAreaConstants';
import I18N from '@/i18n';
import { TEAM_QUOTA_EXCEED } from '@/constants/ErrorCode';
import { shopCheckNameExistsGet, shopLanProxyPut } from '@/services/api-ShopAPI/ShopController';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import buttonStyles from '@/style/button.less';
import { openUpgradeModal } from '@/pages/Setting/components/UpgradeModal';
import { useRequest } from '@@/plugin-request/request';

type Props = {
  onUpdate: () => void;
};

/**
 * 创建店铺
 * @param props
 * @constructor
 */
const CreateShopGuide = (props: Props) => {
  const { onUpdate } = props;
  const [visible, changeVisible] = useState(true);
  const [type, changeType] = useState<'Local' | 'Global'>('Local');
  const [form] = Form.useForm();
  const usernameRef = useRef<UsernameFieldInstance>();
  const [area, changeArea] = useState<string>('');
  const { run: submit, loading: submitting } = useRequest(
    async () => {
      const values = await form.validateFields();
      const { platformIds, username, password, accu, ...others } = trimValues(values);
      usernameRef.current?.applyChange();
      return tkShopPost(
        {
          platformIds: isArray(platformIds) ? platformIds : [platformIds],
          username,
          password,
          ...others,
          type,
          bindFingerprint: true,
          addPassword: !!username || !!password,
          extraProp: accu ? 'accu' : '',
        },
        SkipErrorNotifyOption,
      )
        .then((res) => {
          const shops = res?.data || [];
          const shopIds = shops.map((shop) => shop.id!);
          let _value = type === 'Global' ? 'UseDirect' : 'buy';
          let _open_session = true;
          const areaLabel =
            TkAreaConstants.areaLabels[TkAreaConstants.platformAreaToLocation[area]];
          let texts = (
            <Form
              style={{
                fontSize: 'inherit',
                display: 'flex',
                flexDirection: 'column',
                gap: 24,
              }}
            >
              <div>您现在需要为该店铺绑定一个“{areaLabel}”的IP地址</div>
              <Form.Item name={'_value'} initialValue={_value} noStyle shouldUpdate>
                <Radio.Group
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 16,
                    fontSize: 'inherit',
                  }}
                  onChange={(e) => {
                    _value = e.target.value;
                  }}
                >
                  <div>
                    <Radio value={'buy'}>{I18N.t('购买IP地址')}</Radio>
                    <Typography.Text type={'secondary'}>
                      {I18N.t('如果您没有IP地址，请在花漾中购买，再绑定到店铺')}
                    </Typography.Text>
                    <HelpLink href={'/ip/buyip'} style={{ marginLeft: 24 }}>
                      {I18N.t('如何购买IP？')}
                    </HelpLink>
                  </div>
                  <div>
                    <Radio value={'import'}>{I18N.t('导入已有 IP 地址')}</Radio>
                    <Typography.Text type={'secondary'}>
                      {I18N.t('如果您已有IP地址，请将其导入至花漾，再绑定到店铺')}
                    </Typography.Text>
                    <HelpLink href={'/ip/myip'} style={{ marginLeft: 24 }}>
                      {I18N.t('如何导入IP？')}
                    </HelpLink>
                  </div>
                  <div>
                    <Radio value={'UseSystem'}>{I18N.t('使用系统代理/VPN')}</Radio>
                    <Typography.Text type={'secondary'}>
                      {I18N.t('如果您使用系统代理/VPN，请将此店铺的 IP 隔离设置为“系统代理/VPN”')}
                    </Typography.Text>
                  </div>
                  <div>
                    <Radio value={'UseDirect'}>{I18N.t('使用本机 IP 直连')}</Radio>
                    <Typography.Text type={'secondary'}>
                      {I18N.t(`如果您位于${areaLabel}，可直接使用本机 IP 直连`)}
                    </Typography.Text>
                  </div>
                </Radio.Group>
              </Form.Item>
              <div>{I18N.t('请注意，以上操作会帮您自动打开花漾客户端')}</div>
            </Form>
          );
          if (type === 'Global') {
            const _count = shopIds.length;
            // 跨境店
            texts = (
              <Form
                style={{
                  fontSize: 'inherit',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 24,
                }}
              >
                <div>
                  您创建了拥有{_count}个站点的跨境店铺，您需要为这{_count}
                  个店铺设置访问店铺的网络连接方式
                </div>
                <Form.Item name={'_value'} noStyle initialValue={_value}>
                  <Radio.Group
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 16,
                      fontSize: 'inherit',
                    }}
                    defaultValue={_value}
                    onChange={(e) => {
                      _value = e.target.value;
                    }}
                  >
                    <div>
                      <Radio value={'UseDirect'}>{I18N.t('使用本机 IP 直连')}</Radio>
                      <Typography.Text type={'secondary'}>
                        {I18N.t('跨境店在国内可以直接访问，且无需考虑 IP 隔离')}
                      </Typography.Text>
                    </div>
                    <div>
                      <Radio value={'buy'}>{I18N.t('购买一个香港的 IP ')}</Radio>
                      <Typography.Text type={'secondary'}>
                        {I18N.t(
                          '如果您在国内访问店铺后台过慢，可以考虑购买一个花漾为您提供的香港 IP',
                        )}
                      </Typography.Text>
                    </div>
                  </Radio.Group>
                </Form.Item>

                <Form.Item shouldUpdate noStyle>
                  {(f) => {
                    const disabled = f.getFieldValue('_value') !== 'UseDirect';
                    return (
                      <Checkbox
                        disabled={disabled}
                        defaultChecked={_open_session}
                        onChange={(e) => {
                          _open_session = e.target.checked;
                        }}
                      >
                        帮我直接打开花漾浏览器以登录店铺后台
                      </Checkbox>
                    );
                  }}
                </Form.Item>
              </Form>
            );
          }

          DMConfirm({
            title: 'TikTok 店铺创建成功',
            iconType: 'success',
            width: 760,
            footerBtns: [
              {
                key: 'ok',
                label: '确定',
                btnProps: {
                  type: 'primary',
                },
                onClick: async () => {
                  const shopId = shopIds[0];
                  if (type === 'Global') {
                    // 跨境店
                    if (_value === 'UseDirect') {
                      await shopLanProxyPut({
                        shopIdList: shopIds,
                        networkType: 'UseDirect',
                        probeOnSession: true,
                        enabled: true,
                      });
                      onUpdate();
                      if (_open_session) {
                        if (shopIds.length == 1) {
                          await visitShop(shopIds[0], 'BROWSER');
                        } else {
                          await openClient({
                            action: 'openUrl',
                            method: 'post',
                            url: `/team/${getCurrentTeamId()}/shopManage/all/`,
                          });
                        }
                      }
                    } else {
                      onUpdate();
                      if (shopIds.length == 1) {
                        // 购买
                        await openClient({
                          action: 'openUrl',
                          method: 'post',
                          url: `/team/${getCurrentTeamId()}/shopManage/all/${shopId}?openIpMarket=true&autoBindShopIds=${shopId}`,
                        });
                      } else {
                        await openClient({
                          action: 'openUrl',
                          method: 'post',
                          url: `/team/${getCurrentTeamId()}/shopManage/all/`,
                        });
                      }
                    }
                  } else {
                    // 本土店
                    if (_value === 'UseSystem' || _value === 'UseDirect') {
                      await shopLanProxyPut({
                        shopIdList: shopIds,
                        networkType: _value === 'UseSystem' ? 'UseSystem' : 'UseDirect',
                        probeOnSession: true,
                        enabled: true,
                      });

                      onUpdate();
                      await visitShop(shopIds[0]);
                    } else if (_value === 'buy') {
                      onUpdate();
                      // 购买
                      await openClient({
                        action: 'openUrl',
                        method: 'post',
                        url: `/team/${getCurrentTeamId()}/shopManage/all/${shopId}?openIpMarket=true&autoBindShopIds=${shopId}`,
                      });
                    } else {
                      onUpdate();
                      await visitShop(shopIds[0]);
                    }
                  }
                },
              },
              {
                key: 'cancel',
                label: '取消',
                onClick() {
                  onUpdate();
                },
              },
            ],
            content: (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 12,
                  fontSize: 14,
                }}
              >
                {texts}
              </div>
            ),
          });
          changeVisible(false);
        })
        .catch((err) => {
          if (err?.data?.code === TEAM_QUOTA_EXCEED) {
            const _confirm = DMConfirm({
              type: 'confirm',
              iconType: 'info',
              okText: I18N.t('在线客服'),
              title: I18N.t('TikTok店铺数量已经达到团队最大配额'),
              content: err.message || I18N.t('请联络官网客服咨询进一步信息'),
              footerBtns: [
                {
                  key: 'upgrade',
                  content: (
                    <Button
                      className={buttonStyles.successBtn}
                      onClick={() => {
                        changeVisible(false);
                        _confirm.destroy();
                        openUpgradeModal();
                      }}
                    >
                      {I18N.t('我要扩容')}
                    </Button>
                  ),
                },
                {
                  key: 'service',
                  content: (
                    <Button
                      type={'primary'}
                      onClick={() => {
                        changeVisible(false);
                        _confirm.destroy();
                        onlineService();
                      }}
                    >
                      {I18N.t('在线客服')}
                    </Button>
                  ),
                },
                {
                  key: 'cancel',
                  content: (
                    <Button
                      type={'default'}
                      onClick={() => {
                        changeVisible(false);
                        _confirm.destroy();
                      }}
                    >
                      {I18N.t('知道了')}
                    </Button>
                  ),
                },
              ],
            });
          } else {
            message.error(err.message);
          }
        });
    },
    {
      manual: true,
    },
  );

  return (
    <DMModal
      width={600}
      open={visible}
      onOk={submit}
      confirmLoading={submitting}
      bodyStyle={{ paddingBottom: 0, paddingTop: 12 }}
      onCancel={() => {
        changeVisible(false);
      }}
      title="添加TikTok店铺"
    >
      <Alert
        style={{ marginBottom: 8 }}
        showIcon
        message={
          <>
            在此处添加的TikTok店铺，本质上对应着花漾客户端中的一个浏览器分身，当访问店铺时会打开花漾浏览器并通过花漾浏览器运营管理您的店铺
            <HelpLink href="/tkshop2/buy#create" style={{ marginLeft: '1em' }} />
          </>
        }
      />
      <DMFormItemContext.Provider value={{ disableLabelMuted: true, labelWidth: 100 }}>
        <Form preserve={false} requiredMark={false} form={form}>
          <DMFormItem label={'店铺类型'}>
            <Radio.Group
              value={type}
              onChange={(e) => {
                const value = e.target.value;
                if (value === 'Local') {
                  form.setFieldsValue({ platformIds: undefined });
                } else if (value === 'Global') {
                  form.setFieldsValue({ platformIds: [] });
                }
                changeArea('');
                changeType(value);
              }}
            >
              <Radio value={'Local'}>本土店</Radio>
              <Radio value={'Global'} style={{ marginLeft: 50 }}>
                跨境店
                <a style={{ marginLeft: 4 }}>
                  <HelpTooltip title="如果您的跨境店支持多个站点，您需要为其创建多个账号并分别指定不同的站点（支持多选），请注意，这会占据当前套餐的店铺数量配额" />
                </a>
              </Radio>
            </Radio.Group>
          </DMFormItem>
          <DMFormItem
            label={'所属站点'}
            name={'platformIds'}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <TkRegionSelector
              onChange={(value, option) => {
                changeArea(option?.area || '');
              }}
              allowClear={type === 'Global'}
              mode={type === 'Local' ? undefined : 'multiple'}
            />
          </DMFormItem>
          {type === 'Local' && area === 'United_States' && (
            <DMFormItem
              label={'Accu'}
              name={'accu'}
              tooltip={
                <Space direction={'vertical'}>
                  <div>Accu店是一种特殊的本土店，可以理解为中资美国企业店。</div>
                  <div>
                    注册条件包括美国营业执照、本土发货、中国法人占股25%以上以及亚马逊年流水200万美金以上。
                  </div>
                  <div>如果您确认您的店铺是Accu店，则勾选此项</div>
                </Space>
              }
              valuePropName={'checked'}
              initialValue={false}
            >
              <Checkbox />
            </DMFormItem>
          )}
          <DMFormItem
            label={'店铺名称'}
            name={'name'}
            initialValue={''}
            rules={[
              {
                validator: (rule, value) => {
                  const val = (value || '').trim();
                  return new Promise<void>((resolve, reject) => {
                    validate
                      .account(val, I18N.t('请输入店铺名称'))
                      .then(() => {
                        shopCheckNameExistsGet({
                          name: val,
                        })
                          .then((res) => {
                            if (res.data) {
                              reject(
                                new Error(I18N.t('名称已存在（请注意检查分身回收站的分身名称）')),
                              );
                            } else {
                              resolve();
                            }
                          })
                          .catch(reject);
                      })
                      .catch(reject);
                  });
                },
              },
            ]}
          >
            <Input autoFocus />
          </DMFormItem>
          <DMFormItem label={'备注'} name={'description'}>
            <Input.TextArea style={{ resize: 'none', height: 130 }} />
          </DMFormItem>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};

export default CreateShopGuide;
