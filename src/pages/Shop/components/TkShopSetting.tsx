import DMModal from '@/components/Common/Modal/DMModal';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { FormItemProps } from 'antd';
import { message } from 'antd';
import { Typography } from 'antd';
import { Checkbox, Space } from 'antd';
import { Button, Form, Input } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { trimValues } from '@/utils/utils';
import {
  shopSettingsByShopIdBasicInfoPut,
  shopSettingsByShopIdPasswordsGet,
} from '@/services/api-ShopAPI/ShopSettingsController';
import TkRegionSelector, { getSupportedAreas } from '@/components/Common/Selector/TkRegionSelector';
import { useRequest } from 'umi';
import { shopByShopIdGet } from '@/services/api-ShopAPI/ShopController';
import type { UsernameFieldInstance } from '@/pages/Shop/components/UsernameField';
import UsernameField from '@/pages/Shop/components/UsernameField';
import validate from '@/utils/validate';
import IconFontIcon from '@/components/Common/IconFontIcon';
import I18N from '@/i18n';
import _ from 'lodash';
import {
  tkShopAreaListByCoordinateIdByCoordinateIdGet,
  tkShopByPlatformsPost,
} from '@/services/api-TKAPI/TkShopController';
import DMConfirm from '@/components/Common/DMConfirm';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';

const AddNewSiteModal = (props: { shop: API.ShopDetailVo; onUpdate: () => void }) => {
  const { onUpdate, shop } = props;
  const [visible, changeVisible] = useState(true);
  const [form] = Form.useForm();
  const { data, loading } = useRequest(() => {
    return tkShopAreaListByCoordinateIdByCoordinateIdGet({
      coordinateId: shop.coordinateId!,
    });
  });
  const { run: submit, loading: submitting } = useRequest(
    async () => {
      const values = trimValues(await form.validateFields());
      await tkShopByPlatformsPost({
        shopId: shop.id,
        ...values,
      });
      changeVisible(false);
      message.success(I18N.t('新的站点已添加'));
      onUpdate();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      bodyStyle={{ paddingBottom: 0 }}
      onOk={submit}
      onCancel={() => {
        changeVisible(false);
      }}
      confirmLoading={submitting}
      open={visible}
      headless
    >
      <Form form={form} requiredMark={false}>
        <Form.Item
          label={'为跨境店增加新的站点'}
          name={'platformIds'}
          rules={[
            {
              required: true,
              message: '请选择需要添加的站点',
            },
          ]}
        >
          {data ? (
            <TkRegionSelector
              maxTagCount={22}
              loading={loading}
              notFoundContent={'所有区域均已添加'}
              allowClear
              mode={'multiple'}
              filter={(endpoint) => {
                return !data?.includes(endpoint.area!);
              }}
            />
          ) : (
            <IconFontIcon iconName={'loading_24'} />
          )}
        </Form.Item>
      </Form>
    </DMModal>
  );
};

const TkShopSetting = (
  props: GhostModalWrapperComponentProps & { id: number; onUpdate: () => void },
) => {
  const { id, onUpdate, modalProps } = props;
  const [visible, changeVisible] = useState(true);
  const [area, changeArea] = useState<string | undefined>('');
  const { data, run: fetch } = useRequest(async () => {
    const { data: shop } = await shopByShopIdGet({ shopId: id });
    const { data: loginData } = await shopSettingsByShopIdPasswordsGet({
      shopId: id,
    }).then((res) => {
      const target = _.find(res?.data, (item) => item.platformId === shop?.platformId);
      if (target) {
        return {
          data: {
            username: target.usernameValue,
            password: target?.passwordValue,
          },
        };
      }
      return {
        data: {
          username: '',
          password: '',
        },
      };
    });
    changeArea(shop?.platform?.area);
    return {
      data: {
        ...(shop || {}),
        accu:
          shop?.platform?.area === 'United_States' &&
          shop.extraProp === 'accu' &&
          shop.type === 'Local',
        ...loginData,
      },
    };
  });
  const afterSubmit = useCallback(() => {
    onUpdate();
    changeVisible(false);
  }, [onUpdate]);
  const [baseForm] = Form.useForm();
  const usernameRef = useRef<UsernameFieldInstance>();

  useEffect(() => {
    if (data) {
      baseForm.setFieldsValue({ ...data });
    }
  }, [baseForm, data]);

  const shopType = useMemo(() => {
    return (
      <DMFormItem label={'店铺类型'} shouldUpdate>
        {data?.type === 'Local' ? '本土店' : '跨境店'}
      </DMFormItem>
    );
  }, [data?.type]);
  const { run: addNewSite, loading } = useRequest(
    async () => {
      if (data) {
        const _supports = await getSupportedAreas();
        await tkShopAreaListByCoordinateIdByCoordinateIdGet({
          coordinateId: data.coordinateId!,
        }).then((res) => {
          if (res.data?.length >= _supports.length) {
            DMConfirm({
              title: '所有支持的区域均已添加',
              type: 'info',
            });
          } else {
            GhostModalCaller(
              <AddNewSiteModal
                shop={data}
                onUpdate={() => {
                  fetch();
                  onUpdate();
                }}
              />,
            );
          }
        });
      }
    },
    {
      manual: true,
    },
  );
  const site = useMemo(() => {
    const _props: Omit<FormItemProps, 'children'> = {
      name: 'platformId',
      rules: [
        {
          required: true,
        },
      ],
      tooltip: data?.type === 'Global' ? '跨境店暂不支持修改区域' : undefined,
    };
    return (
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
        <DMFormItem style={{ flex: 1 }} label={'所属站点'} {..._props}>
          <TkRegionSelector
            disabled={data?.type === 'Global'}
            onChange={(value, option) => {
              changeArea(option?.area);
            }}
          />
        </DMFormItem>
        {data?.type === 'Global' && (
          <Typography.Link
            style={{ lineHeight: '32px' }}
            disabled={!data}
            onClick={() => {
              if (loading) {
                return;
              }
              addNewSite();
            }}
          >
            <Space>
              <IconFontIcon iconName={'tianjia_24'} />
              <span>{I18N.t('增加新的站点')}</span>
            </Space>
          </Typography.Link>
        )}
      </div>
    );
  }, [addNewSite, data, loading]);
  const shopName = useMemo(() => {
    return (
      <DMFormItem
        label={'店铺名称'}
        name={'name'}
        rules={[
          {
            validator(rule, value) {
              return validate.account(value, '店铺名称');
            },
          },
        ]}
      >
        <Input autoComplete={'new-password'} />
      </DMFormItem>
    );
  }, []);
  const password = useMemo(() => {
    return (
      <DMFormItem label={'密码'} name={'password'}>
        <Input.Password autoComplete={'new-password'} />
      </DMFormItem>
    );
  }, []);
  const description = useMemo(() => {
    return (
      <DMFormItem label={'备注'} name={'description'}>
        <Input.TextArea
          rows={3}
          style={{
            resize: 'none',
          }}
        />
      </DMFormItem>
    );
  }, []);

  const footer = useMemo(() => {
    return (
      <>
        <Button
          type={'primary'}
          onClick={() => {
            baseForm.submit();
          }}
        >
          确定
        </Button>
        <Button
          onClick={() => {
            changeVisible(false);
          }}
        >
          取消
        </Button>
      </>
    );
  }, [baseForm]);

  return (
    <DMModal
      width={600}
      footer={footer}
      title={'店铺属性'}
      bodyStyle={{ paddingBottom: 0 }}
      open={visible}
      onCancel={() => {
        changeVisible(false);
      }}
      {...modalProps}
    >
      <DMFormItemContext.Provider value={{ labelWidth: 95, disableLabelMuted: true }}>
        <Form
          preserve={false}
          requiredMark={false}
          initialValues={data}
          layout={'horizontal'}
          onFinish={async () => {
            usernameRef?.current?.applyChange();
            const vals = trimValues(await baseForm.validateFields());
            await shopSettingsByShopIdBasicInfoPut({
              shopId: id!,
              passwordSet: true,
              ...vals,
              name: vals.name!,
              extraProp: vals.accu ? 'accu' : '',
            });
            afterSubmit();
          }}
          form={baseForm}
        >
          {shopType}
          {site}
          {shopName}
          {data?.type === 'Local' && area === 'United_States' && (
            <DMFormItem
              label={'Accu'}
              name={'accu'}
              tooltip={
                <Space direction={'vertical'}>
                  <div>Accu店是一种特殊的本土店，可以理解为中资美国企业店。</div>
                  <div>
                    注册条件包括美国营业执照、本土发货、中国法人占股25%以上以及亚马逊年流水200万美金以上。
                  </div>
                  <div>如果您确认您的店铺是Accu店，则勾选此项</div>
                </Space>
              }
              valuePropName={'checked'}
            >
              <Checkbox />
            </DMFormItem>
          )}
          <DMFormItem label={false} shouldUpdate noStyle>
            {(f) => {
              return (
                <UsernameField ref={usernameRef} form={f} username={data?.username} editable />
              );
            }}
          </DMFormItem>
          {password}
          {description}
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
export default TkShopSetting;
