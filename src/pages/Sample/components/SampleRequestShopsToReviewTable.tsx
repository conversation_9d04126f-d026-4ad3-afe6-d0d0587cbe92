import type { CSSProperties } from 'react';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import type { Column } from '@/hooks/useResizableColumns';
import useResizableColumns from '@/hooks/useResizableColumns';
import I18N from '@/i18n';
import _ from 'lodash';
import EventEmitter from 'events';
import type { ActionType } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import TableColumnsSettingModalTrigger, {
  getColumnByDataIndex,
} from '@/components/SortableTransfer/TableColumnsSettingModalTrigger';
import SortDropdown, { useOrder } from '@/components/Sort/SortDropdown';
import { Button, Form, Space, Tooltip, Typography } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { dateFormat, getTeamIdFromUrl } from '@/utils/utils';
import Placeholder from '@/components/Common/Placeholder';
import { history } from 'umi';
import { ShopDetailNode } from '@/components/Common/ShopNode';
import constants from '@/constants';
import { getShopStatus, triggerShopTask } from '@/pages/Shop/components/utils';
import { StyledInlineForm, StyledLayout } from '@/style/styled';
import { tkshopSampleRequestShopsToReviewGet } from '@/services/api-TKShopAPI/TkshopSampleRequestController';
import { shopByCategoryByCategoryGet } from '@/services/api-ShopAPI/ShopController';

const eventEmitter = new EventEmitter();

type SampleShopColumn =
  | keyof API.ShopBriefVo
  | keyof Pick<API.SampleRequestShopBriefVo, 'lastSyncTime' | 'count'>
  | 'shopLastSyncTime'
  | 'option';

const DEFAULT_COLUMNS: SampleShopColumn[] = [
  'name',
  'platform',
  'description',
  'count',
  'lastSyncTime',
  'option',
];
function useSampleShopsColumns(options: { order: any; changeOrder: any }) {
  const { order, changeOrder } = options;
  const meta: Pick<
    Column<any>,
    'dataIndex' | 'title' | 'resizable' | 'sortable' | 'width' | 'disabled'
  >[] = useMemo(() => {
    return [
      {
        dataIndex: 'name',
        title: I18N.t('店铺名称'),
        disabled: true,
        resizable: {
          minWith: 150,
        },
      },
      {
        dataIndex: 'platform',
        title: I18N.t('站点'),
        width: 100,
      },
      {
        dataIndex: 'createTime',
        title: I18N.t('店铺创建时间'),
        width: 150,
        sortable: {},
      },

      {
        dataIndex: 'description',
        title: I18N.t('店铺备注'),
        resizable: {
          minWith: 100,
        },
      },
      {
        dataIndex: 'count',
        title: I18N.t('待审批的索样记录'),
        width: 140,
        // sortable: {},
      },
      {
        dataIndex: 'lastSyncTime',
        title: I18N.t('索样记录更新时间'),
        width: 160,
        // sortable: {},
      },
      {
        dataIndex: 'type',
        title: I18N.t('店铺类型'),
        width: 80,
      },
      {
        dataIndex: 'loginStatus',
        title: I18N.t('店铺状态'),
        width: 160,
      },
      {
        dataIndex: 'option',
        title: I18N.t('操作'),
        disabled: true,
        width: 175,
      },
    ];
  }, []);
  const storage_key = `tkshop_sample_shops_columns_V20250728`;
  const getColumn = useCallback(
    (key: SampleShopColumn) => {
      const target = getColumnByDataIndex(key, meta);
      if (!target) {
        return;
      }
      const { title, ...rest } = target;
      const base = {
        dataIndex: key,
        title,
        ellipsis: false,
        ...rest,
      };
      switch (key) {
        case 'name':
          return {
            ...base,
            render: (_text, record) => {
              const { shop } = record;
              return (
                <ShopDetailNode
                  data={shop}
                  onClick={() => {
                    history.push(`/team/${getTeamIdFromUrl()}/sample/${shop.id}`);
                  }}
                />
              );
            },
          };
        case 'count': {
          return {
            ...base,
            render(_text, record) {
              const { shop, count } = record;
              return (
                <Typography.Link
                  ellipsis
                  onClick={() => {
                    history.push(`/team/${getTeamIdFromUrl()}/sample/${shop.id}`);
                  }}
                >
                  {count}
                </Typography.Link>
              );
            },
          };
        }
        case 'platform':
          return {
            ...base,
            render: (_text, record) => {
              const { shop } = record;
              return constants.Area[shop.platform?.area];
            },
          };
        case 'type':
          return {
            ...base,
            render: (_text, record) => {
              const { shop } = record;
              const { type } = shop;
              if (type === 'Local') {
                return I18N.t('本土店');
              }
              return I18N.t('跨境店');
            },
          };
        case 'loginStatus':
          return {
            ...base,
            render(_text, record) {
              const { shop } = record;
              return getShopStatus(shop);
            },
          };
        case 'createTime':
          return {
            ...base,
            render(_text, record) {
              const { createTime } = record.shop!;
              if (!createTime) {
                return <Placeholder />;
              }
              return (
                <Typography.Text ellipsis>
                  {dateFormat(createTime, 'YYYY-MM-DD HH:mm')}
                </Typography.Text>
              );
            },
          };
        case 'lastSyncTime':
          return {
            ...base,
            render(_text, record) {
              const { lastSyncTime } = record;
              if (!lastSyncTime) {
                return <Placeholder />;
              }
              return (
                <Typography.Text ellipsis>
                  {dateFormat(lastSyncTime, 'YYYY-MM-DD HH:mm')}
                </Typography.Text>
              );
            },
          };
        case 'description': {
          return {
            ...base,
            render(_text, record) {
              const { shop } = record;
              const { description } = shop;
              if (!description) {
                return <Placeholder />;
              }
              return (
                <Typography.Text ellipsis={{ tooltip: description }}>{description}</Typography.Text>
              );
            },
          };
        }
        case 'option':
          return {
            ...base,
            render(_text, record) {
              const { shop } = record;
              return (
                <Space size={8}>
                  <Button
                    size={'small'}
                    type={'primary'}
                    onClick={() => {
                      triggerShopTask([shop], 'TS_SyncSampleCreator');
                    }}
                  >
                    <span>{I18N.t('立即同步')}</span>
                  </Button>
                  <Button
                    size={'small'}
                    type={'primary'}
                    onClick={() => {
                      history.push(`/team/${getTeamIdFromUrl()}/sample/${shop.id}`);
                    }}
                  >
                    {I18N.t('进入审批')}
                  </Button>
                </Space>
              );
            },
          };
        default:
          return base;
      }
    },
    [meta],
  );
  const getStorageValue = useCallback(() => {
    let _columns = DEFAULT_COLUMNS;
    try {
      if (localStorage.getItem(storage_key)) {
        _columns = JSON.parse(
          localStorage.getItem(storage_key) || '[]',
        ) as unknown as SampleShopColumn[];
        _columns = _columns.filter((key) => {
          return (
            _.findIndex(meta, (item) => {
              return item.dataIndex === key;
            }) !== -1
          );
        });
        if (!_columns.length) {
          _columns = DEFAULT_COLUMNS;
        }
      }
    } catch (e) {
      console.log(e);
    }
    return _columns;
  }, [meta, storage_key]);
  const [visibleColumns, setVisibleColumns] = useState<SampleShopColumn[]>(getStorageValue());
  const onVisibleColumnsChange = useCallback(() => {
    setVisibleColumns(getStorageValue());
  }, [getStorageValue]);
  useEffect(() => {
    eventEmitter.on('UPDATE', onVisibleColumnsChange);
    return () => {
      eventEmitter.off('UPDATE', onVisibleColumnsChange);
    };
  }, [onVisibleColumnsChange]);
  const tableColumns = useMemo(() => {
    return visibleColumns
      .filter((key) => {
        return (
          _.findIndex(meta, (item) => {
            return item.dataIndex === key;
          }) !== -1 && getColumn(key)
        );
      })
      .map(getColumn);
  }, [getColumn, meta, visibleColumns]);
  const { columns, tableWidth, header, isInitialized } = useResizableColumns({
    columns: tableColumns,
    fixWidth: 32,
    order,
    changeOrder,
    scope: 'tkshop_sample_shops_v20250728',
  });
  const tableProps = useMemo(() => {
    return {
      columns,
      width: tableWidth,
      header,
    };
  }, [columns, header, tableWidth]);
  const sortableColumns = useMemo(() => {
    return meta.filter((item) => {
      return item.sortable;
    });
  }, [meta]);
  const _updateColumns = useCallback(
    (cols: string[]) => {
      localStorage.setItem(storage_key, JSON.stringify(cols));
      eventEmitter.emit('UPDATE');
    },
    [storage_key],
  );
  return {
    visibleColumns,
    sortableColumns,
    tableProps,
    update: _updateColumns,
    isInitialized,
    meta,
  };
}

//待审批的店铺
const SampleRequestShopsToReviewTable = forwardRef((props: { style?: CSSProperties }, ref) => {
  const { style } = props;
  const actionRef = useRef<ActionType>();
  const [selected, setSelected] = useState<API.ShopBriefVo[]>([]);
  const { order, changeOrder } = useOrder(
    {
      key: 'createTime',
      ascend: false,
    },
    `tkshop_sample_shops_order_V20250728`,
  );
  const { visibleColumns, sortableColumns, tableProps, update, meta, isInitialized } =
    useSampleShopsColumns({
      order,
      changeOrder,
    });
  useImperativeHandle(ref, () => {
    return actionRef.current;
  });
  const header = useMemo(() => {
    return (
      <StyledInlineForm style={{ flex: 1 }}>
        <Form.Item noStyle>
          <TableColumnsSettingModalTrigger columns={visibleColumns} meta={meta} onSubmit={update} />
        </Form.Item>
        <Form.Item noStyle>
          <SortDropdown
            list={sortableColumns.map((item) => {
              return {
                key: item.dataIndex,
                label: item.title,
              };
            })}
            order={order}
            onChange={changeOrder}
          />
        </Form.Item>
      </StyledInlineForm>
    );
  }, [changeOrder, meta, order, sortableColumns, update, visibleColumns]);
  return (
    <StyledLayout style={style}>
      <div className={'header'}>{header}</div>
      <div className="main">
        <ProTable<API.SampleRequestShopBriefVo>
          actionRef={actionRef}
          {...scrollProTableOptionFn({
            scroll: {
              x: tableProps?.width,
            },
            alwaysShowFooter: true,
            footer: () => {
              const node = (
                <Button
                  ghost
                  disabled={!selected.length}
                  type={'primary'}
                  icon={<IconFontIcon iconName={'duzhanshifangwen_24'} />}
                  onClick={() => {
                    triggerShopTask(selected, 'TS_SyncSampleCreator');
                  }}
                >
                  {I18N.t('索样记录同步')}
                </Button>
              );
              if (selected.length) {
                return node;
              }
              return (
                <Tooltip placement={'topLeft'} title={I18N.t('请选择您要操作的店铺')}>
                  <span>{node}</span>
                </Tooltip>
              );
            },
            pageId: 'sample_shops_table_pageId',
          })}
          columns={tableProps?.columns}
          components={{ header: tableProps?.header }}
          rowSelection={{
            selectedRowKeys: selected.map((item) => {
              return item.id!;
            }),
            onChange: (_keys, selectedRows) => {
              setSelected(
                selectedRows?.map((item) => {
                  return item.shop!;
                }),
              );
            },
          }}
          rowKey={(record, index) => {
            return record?.shop?.id || index;
          }}
          request={async (params) => {
            const { current, pageSize } = params;
            const countMap = await tkshopSampleRequestShopsToReviewGet().then((res) => {
              const count = {};
              res.data?.forEach((item) => {
                count[item.shopId!] = item;
              });
              return count;
            });
            return shopByCategoryByCategoryGet({
              category: 'all',
              platformTypes: 'TikTok',
              sortFiled: order?.key,
              sortOrder: order?.ascend ? 'asc' : 'desc',
              pageSize,
              pageNum: current,
            }).then((res) => {
              const list = res.data?.list || [];
              return {
                data: list.map((item) => {
                  return {
                    shop: item,
                    ...(countMap[item.id] || {}),
                  };
                }),
                total: res.data?.total || 0,
              };
            });
            // return tkshopSampleRequestPageShopsToReviewGet({
            //   sortFiled: order?.key,
            //   sortOrder: '',
            //   pageSize,
            //   pageNum: current,
            // }).then((res) => {
            //   const list = res.data?.list || [];
            //   return {
            //     data: list,
            //     total: res.data?.total || 0,
            //   };
            // });
          }}
          style={
            !isInitialized
              ? {
                  opacity: 0,
                  pointerEvents: 'none',
                }
              : {}
          }
        />
      </div>
    </StyledLayout>
  );
});
export default SampleRequestShopsToReviewTable;
