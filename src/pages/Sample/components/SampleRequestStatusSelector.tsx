import { Form, Checkbox, Menu, Dropdown } from 'antd';
import _ from 'lodash';
import { OptionPlaceholder } from '@/components/Common/Placeholder';
import Selector from '@/components/Common/Selector';
import I18N from '@/i18n';
import styled from 'styled-components';
import { useMemo, useState } from 'react';

const StyledMenu = styled(Menu)`
  && {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    .ant-menu-item {
      height: 32px !important;
      margin: 0 !important;
      padding: 0 !important;
      line-height: 32px !important;
    }
    .ant-menu-title-content {
      height: 100%;
      > div {
        display: flex;
        gap: 4px;
        align-items: center;
        height: 100%;
        padding: 5px 12px;
        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }
`;

const SampleRequestStatusSelector = (props: {
  value?: string[] | undefined;
  onChange?: (value: string[] | undefined) => void;
}) => {
  const { value = [], onChange } = props;
  const [visible, setVisible] = useState(false);
  const _options = useMemo(() => {
    return [
      { label: '待审批', value: 'ToReview' },
      { label: '待发货', value: 'ReadyToShip' },
      { label: '已发货', value: 'Shipped' },
      { label: '待产出', value: 'InProgress' },
      { label: '已完成', value: 'Completed' },
      { label: '已取消', value: 'Cancelled' },
      { label: '已过期', value: 'Expired' },
    ];
  }, []);
  return (
    <Form.Item shouldUpdate noStyle>
      {() => {
        let text = undefined;
        if (value.length > 0) {
          if (value?.length === 1) {
            // 找到对应的label
            text = _options.find((item) => item.value === value[0])?.label;
          } else {
            text = I18N.t(`指定状态范围`);
          }
        }

        return (
          <Dropdown
            arrow
            open={visible}
            onDropdownVisibleChange={(v) => {
              setVisible(v);
            }}
            placeholder={<OptionPlaceholder type={'sample_request'} text={text} />}
            dropdownRender={() => {
              return (
                <StyledMenu
                  selectable={false}
                  items={_options.map((item) => {
                    const checked = value?.includes(item.value);
                    return {
                      key: item.value,
                      label: (
                        <div
                          onClick={() => {
                            let list: string[] = [];
                            if (checked) {
                              list = _.without(value, item.value);
                            } else {
                              list = [...value, item.value];
                            }
                            if (list.length === _options.length || list.length === 0) {
                              onChange?.(undefined);
                              setVisible(false);
                            } else {
                              onChange?.(list);
                            }
                          }}
                        >
                          <Checkbox checked={checked} />
                          <span>{item.label}</span>
                        </div>
                      ),
                    };
                  })}
                />
              );
            }}
          />
        );
      }}
    </Form.Item>
  );
};
export default SampleRequestStatusSelector;
