import { Form } from 'antd';
import CreatorAvatarAndName from '@/components/Common/CreatorAvatarAndName';
import DMFormItem from '@/components/Common/DMFormItem';
import I18N from '@/i18n';
import styled from 'styled-components';
import type { CSSProperties, ReactNode } from 'react';
import { useMemo } from 'react';
import MessageIntervalFormItem from '@/pages/TikTok/components/MessageIntervalFormItem';

const StyledFormItem = styled(DMFormItem)`
  overflow: hidden !important;
  > .ant-row {
    flex-wrap: nowrap !important;
    overflow: hidden !important;
  }
  .ant-form-item-control {
    overflow: hidden !important;
  }
`;

const SelectedTargetFormItem = (props: {
  data: API.TkshopCreatorDetailVo[];
  style?: CSSProperties;
  label?: ReactNode;
  showSettings?: boolean;
}) => {
  const { data, style, label = I18N.t('指定达人'), showSettings } = props;
  const trigger = useMemo(() => {
    if (!showSettings) {
      return false;
    }
    return <MessageIntervalFormItem />;
  }, [showSettings]);

  return (
    <StyledFormItem label={label} style={style}>
      <Form.Item shouldUpdate noStyle>
        {() => {
          const count = data.length;
          if (data.length > 1) {
            return (
              <div
                style={{
                  display: 'inline-flex',
                  maxWidth: '100%',
                  overflow: 'hidden',
                  flexWrap: 'nowrap',
                  gap: 16,
                  alignItems: 'center',
                }}
              >
                {I18N.t('{{count}}个', {
                  count: count.toString(),
                })}
                {trigger}
              </div>
            );
          }
          const creator = data[0];
          return (
            <div
              style={{
                display: 'inline-flex',
                maxWidth: '100%',
                overflow: 'hidden',
                flexWrap: 'nowrap',
                alignItems: 'center',
                gap: 16,
              }}
            >
              <CreatorAvatarAndName creator={creator} />
              {trigger}
            </div>
          );
        }}
      </Form.Item>
    </StyledFormItem>
  );
};
export default SelectedTargetFormItem;
