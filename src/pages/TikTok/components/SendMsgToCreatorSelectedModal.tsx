import { useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Button, Col, Collapse, Form, Row, Space, Typography } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { SelectYunPanFileField } from '@/pages/RpaFlows/components/items/SelectYunPanFileField';
import ProductInputTextArea from '@/pages/RpaFlows/components/ProductInputTextArea';
import { SpeechConfigFields } from '@/pages/RpaFlows/components/forms/ContactInfo';
import TkAreaConstants from '@/constants/TkAreaConstants';
import { autoOpenErrorField, useTaskPoolAddAnimation } from '@/hooks/interactions';
import { useRequest } from '@@/plugin-request/request';
import { trimValues } from '@/utils/utils';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import { tkshopJobsSendImChatByHandlePost } from '@/services/api-TKShopAPI/TkShopJobController';
import {
  getContentFromSpeechConfig,
  getWordIdsFromSpeechConfig,
} from '@/pages/Setting/components/SendInviteFormFields';
import { transformCreatorsForJob } from '@/pages/Creator/utils';
import ShopFormField from '@/pages/RpaFlows/components/items/ShopFormField';
import buttonStyles from '@/style/button.less';
import { TaskShelfJobIcon, useAddTask } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { getJobPreserveKeyByShop, usePreserveParams } from '@/pages/Schedule/components/util';
import SelectedTargetFormItem from '@/pages/TikTok/components/SelectedTargetFormItem';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { tkshopSystemStatusGet } from '@/services/api-TKShopAPI/TkshopSystemController';
import { tkshopCountTodayInteractionsGet } from '@/services/api-TKShopAPI/TkshopInteractionController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import HelpLink from '@/components/HelpLink';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import FlowReadmeAsidePanel, {
  FlowReadmeAsidePanelWidth,
} from '@/components/Common/MarkdownView/FlowReadmeAsidePanel';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import { StyledCollapse } from '@/style/styled';
import AutoTagFormItems from '@/pages/TikTok/components/AutoTagFormItems';

export const SendMsgByHandleFields = (props: {
  shop?: API.ShopBriefVo;
  taskType?: 'TS_VideoADCode' | 'TS_IMChatByHandle';
  height?: number;
}) => {
  const { shop, taskType, height } = props;
  return (
    <StyledCollapse accordion ghost destroyInactivePanel={false} defaultActiveKey={'message'}>
      <Collapse.Panel forceRender key={'message'} header={I18N.t('消息内容')}>
        <SpeechConfigFields
          scene={'ShopCreator'}
          showCommentType
          speechType={taskType === 'TS_VideoADCode' ? 'RequestAdCode' : 'SendMsg'}
        />
        <DMFormItem
          label="商品图片"
          name="medias"
          tooltip="可将图片发送给达人，需要注意的是：只允许发送位于花漾云盘中的文件（可在花漾客户端中将图片文件上传至云盘），允许发送多张图片（未回复的达人只允许接收5条消息）"
        >
          <SelectYunPanFileField mode={'multiple'} />
        </DMFormItem>
        <ProductInputTextArea height={height} shop={shop} />
      </Collapse.Panel>
      <Collapse.Panel key={'tags'} header={'达人存储设置'}>
        <AutoTagFormItems type={'send_msg'} />
      </Collapse.Panel>
    </StyledCollapse>
  );
};
const SendMsgToCreatorSelectedModal = (
  props: GhostModalWrapperComponentProps & {
    selected: API.TkshopCreatorDetailVo[];
    region?: string;
    shop?: API.ShopDetailVo;
    refer?: 'list' | 'detail' | 'global';
    taskType?: 'TS_VideoADCode' | 'TS_IMChatByHandle';
    getExtraSettings?: (item: API.TkshopCreatorDetailVo) => Record<string, any>;
  },
) => {
  const {
    selected,
    refer = 'detail',
    region: _region,
    shop: __shop,
    taskType = 'TS_IMChatByHandle',
    getExtraSettings,
    modalProps,
  } = props;
  const [visible, setVisible] = useState(true);
  const region = useMemo(() => {
    if (__shop) {
      return TkAreaConstants.platformAreaToLocation[__shop.platform!.area!];
    }
    if (_region) {
      return _region;
    }
    return selected[0].region!;
  }, [__shop, _region, selected]);
  const [form] = Form.useForm();
  const [shop, setShop] = useState<API.ShopDetailVo>(__shop);
  const { play } = useTaskPoolAddAnimation();
  const { read, write } = usePreserveParams(
    getJobPreserveKeyByShop(
      taskType === 'TS_VideoADCode' ? 'TS_VideoADCode_hack_creator' : taskType,
      shop?.id,
    ),
  );
  const no_device_validate = useRef(false);
  const hasAuth = useAuthJudgeCallback();
  const { data: quotaInfo, run } = useRequest(
    async (shopId) => {
      const _quota = await tkshopSystemStatusGet().then((res) => {
        return res.data?.tkshopTeam?.imChatQuota!;
      });
      if (_quota > 0) {
        const used = await tkshopCountTodayInteractionsGet({
          shopId,
          interactType: 'im_chat',
        }).then((res) => {
          return res.data!;
        });
        const left = Math.max(_quota - used, 0);
        if (left === 0 || selected.length > left) {
          return {
            data: {
              error: true,
              message: I18N.t(`超过可私信数量最大配额（今天可私信${_quota}人，还可私信${left}人）`),
            },
          };
        }
        return {
          data: {
            error: false,
            message: I18N.t(`今天可私信${_quota}人，还可私信${left}人`),
          },
        };
      }
      return {
        data: {
          error: false,
        },
      };
    },
    {
      manual: true,
    },
  );
  const { run: submit, loading } = useRequest(
    async (e: any) => {
      if (!quotaInfo?.error) {
        no_device_validate.current = false;
        const _values = await form.validateFields().catch(autoOpenErrorField);
        if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
          showFunctionCodeAlert();
        } else {
          const { device, shop: _shop, ...rest } = trimValues(_values);
          const advanceSettings = {
            ...rest,
            creators: transformCreatorsForJob(selected, refer, getExtraSettings),
            taskType,
          };
          if (!advanceSettings.bind_manual_tag) {
            delete advanceSettings.manual_tag;
          }
          await tkshopJobsSendImChatByHandlePost({
            advanceSettings,
            wordsIds: getWordIdsFromSpeechConfig(rest.SpeechConfig),
            deviceId: device.deviceId,
            content: getContentFromSpeechConfig(rest.SpeechConfig),
            shopId: _shop.id!,
          });
          write(rest);
          setVisible(false);
          play(e, 'TS_IMChatByHandle');
        }
      }
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (shop?.id) {
      form.resetFields();
      read().then((res) => {
        if (res) {
          form.resetFields();
          form.setFieldsValue(res);
        }
      });
    }
  }, [form, read, shop?.id]);
  const { run: add } = useAddTask();
  const { run: addToTaskShelf, loading: adding } = useRequest(
    async (e: any) => {
      if (!quotaInfo?.error) {
        no_device_validate.current = true;
        const _values = await form.validateFields();
        const { device, shop: _shop, ...rest } = trimValues(_values);
        await add(
          {
            items:
              selected.map((item) => {
                const advanceSettings = {
                  ...rest,
                  creators: transformCreatorsForJob([item], refer, getExtraSettings),
                  taskType,
                };
                if (!advanceSettings.bind_manual_tag) {
                  delete advanceSettings.manual_tag;
                }
                return {
                  creatorId: item.id!,
                  taskType: 'TS_IMChatByHandle',
                  rpaType: 'Browser',
                  accountId: _shop.id,
                  parameter: JSON.stringify({
                    advanceSettings,
                    wordsIds: getWordIdsFromSpeechConfig(rest.SpeechConfig),
                    content: getContentFromSpeechConfig(rest.SpeechConfig),
                  }),
                };
              }) || [],
          },
          e,
        );
        write(rest);
        setVisible(false);
      }
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (shop?.id) {
      run(shop.id);
    }
  }, [run, shop?.id]);
  const footer = useMemo(() => {
    let actions = (
      <Space>
        {refer !== 'global' && (
          <Button
            onClick={addToTaskShelf}
            disabled={!!quotaInfo?.error}
            loading={adding}
            className={buttonStyles.successBtn}
          >
            {I18N.t('放到任务抽屉')}
          </Button>
        )}
        <Button onClick={submit} disabled={!!quotaInfo?.error} loading={loading} type={'primary'}>
          {I18N.t('立即执行')}
        </Button>
        <Button
          type={'default'}
          onClick={() => {
            setVisible(false);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
    if (refer === 'detail') {
      actions = (
        <Space>
          <Button onClick={submit} loading={loading}>
            {I18N.t('立即执行')}
          </Button>
          <Button onClick={addToTaskShelf} loading={adding} type={'primary'}>
            {I18N.t('放到任务抽屉')}
          </Button>
          <Button
            type={'default'}
            onClick={() => {
              setVisible(false);
            }}
          >
            {I18N.t('取消')}
          </Button>
        </Space>
      );
    }

    return (
      <div
        style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {quotaInfo?.message ? (
          <Space size={12}>
            <Space size={4}>
              <IconFontIcon iconName={'info_24'} />
              <Typography.Text type={quotaInfo?.error ? 'danger' : 'secondary'}>
                {quotaInfo?.message}
              </Typography.Text>
            </Space>
            <HelpLink href={'tkshop2/brief#diff'} />
          </Space>
        ) : (
          <span />
        )}
        {actions}
      </div>
    );
  }, [addToTaskShelf, adding, loading, quotaInfo?.error, quotaInfo?.message, refer, submit]);
  return (
    <DMModal
      title={I18N.t('站内消息')}
      bodyStyle={{ paddingBottom: 0 }}
      footer={footer}
      width={840 + FlowReadmeAsidePanelWidth}
      asideWrapperStyle={{
        flex: `0 0 ${FlowReadmeAsidePanelWidth}px`,
        height: 700,
      }}
      asidePanel={<FlowReadmeAsidePanel bizCode={'tkshop.TS_IMChatByHandle'} />}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <Form
        requiredMark={false}
        form={form}
        style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
      >
        <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 130 }}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <DMFormItem
                label={I18N.t('店铺')}
                name={'shop'}
                initialValue={shop}
                rules={[
                  {
                    required: true,
                    message: I18N.t('请选择店铺'),
                  },
                ]}
              >
                <ShopFormField onChange={setShop} area={TkAreaConstants.areas[region!]} />
              </DMFormItem>
            </Col>
            <Col span={12}>
              <DMFormItem
                label="运行设备"
                name={'device'}
                rules={[
                  {
                    validator(_rule, val) {
                      if (!val && !no_device_validate.current) {
                        return Promise.reject(new Error(I18N.t('请选择运行设备')));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <SelectDeviceField />
              </DMFormItem>
            </Col>
          </Row>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <SelectedTargetFormItem style={{ marginBottom: 0 }} showSettings data={selected} />
            </Col>
            <Col span={12}>
              <DMFormItem label={I18N.t('任务类型')} style={{ marginBottom: 0 }}>
                <Space>
                  <ColoursIcon className={TaskShelfJobIcon.TS_IMChatByHandle} />
                  <span>{I18N.t('发送站内消息')}</span>
                </Space>
              </DMFormItem>
            </Col>
          </Row>
          <SendMsgByHandleFields height={65} shop={shop} taskType={taskType} />
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};
export default SendMsgToCreatorSelectedModal;
