import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import { useState } from 'react';
import { Form, InputNumber, Typography } from 'antd';
import DMModal from '@/components/Common/Modal/DMModal';
import DMFormItem from '@/components/Common/DMFormItem';
import I18N from '@/i18n';
import { ProFormText } from '@ant-design/pro-form';

const MessageIntervalModal = (
  props: GhostModalWrapperComponentProps & { value: number; onChange: (val: number) => void },
) => {
  const { value, onChange } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();

  return (
    <DMModal
      width={460}
      closeIcon={() => null}
      open={open}
      onOk={form.submit}
      onCancel={() => {
        setOpen(false);
      }}
      bodyStyle={{ paddingTop: 32 }}
    >
      <Form
        form={form}
        onFinish={({ interval }) => {
          onChange(interval);
          setOpen(false);
        }}
      >
        <DMFormItem
          name={'interval'}
          initialValue={value || 5}
          label={'每笔消息的间隔'}
          rules={[
            {
              validator: (rule, val) => {
                // 1-60s 不能不填
                if (!val) {
                  return Promise.reject(new Error(I18N.t('请输入数字')));
                }
                if (val < 1 || val > 60) {
                  return Promise.reject(new Error(I18N.t('请输入1 - 60的数字')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber style={{ width: 120 }} min={1} precision={0} addonAfter={'秒'} />
        </DMFormItem>
      </Form>
    </DMModal>
  );
};
const MessageIntervalFormItem = () => {
  return (
    <Form.Item shouldUpdate noStyle>
      {(f) => {
        return (
          <div>
            <ProFormText name={'messageInterval'} initialValue={5} hidden />
            <Typography.Link
              style={{ whiteSpace: 'nowrap' }}
              onClick={() => {
                GhostModalCaller(
                  <MessageIntervalModal
                    value={f.getFieldValue('messageInterval')}
                    onChange={(val) => {
                      f.setFieldsValue({ messageInterval: val });
                    }}
                  />,
                );
              }}
            >
              高级设置
            </Typography.Link>
          </div>
        );
      }}
    </Form.Item>
  );
};
export default MessageIntervalFormItem;
