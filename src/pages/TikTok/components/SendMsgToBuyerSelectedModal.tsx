import { useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Button, Col, Form, Radio, Row, Space, Typography } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { SelectYunPanFileField } from '@/pages/RpaFlows/components/items/SelectYunPanFileField';
import ProductInputTextArea from '@/pages/RpaFlows/components/ProductInputTextArea';
import { SpeechConfigFields } from '@/pages/RpaFlows/components/forms/ContactInfo';
import { useTaskPoolAddAnimation } from '@/hooks/interactions';
import { useRequest } from '@@/plugin-request/request';
import { trimValues } from '@/utils/utils';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import { tkshopJobsSendBuyerImChatPost } from '@/services/api-TKShopAPI/TkShopJobController';
import {
  getContentFromSpeechConfig,
  getWordIdsFromSpeechConfig,
} from '@/pages/Setting/components/SendInviteFormFields';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import {
  tkshopBuyerCalcBuyerShopsPost,
  tkshopBuyerCountTodayInteractionsGet,
} from '@/services/api-TKShopAPI/TkshopBuyerController';
import { ShopDetailNode, ShopNodeById } from '@/components/Common/ShopNode';
import { shopByShopIdGet } from '@/services/api-ShopAPI/ShopController';
import { TaskShelfJobIcon, useAddTask } from '@/pages/TikTok/Live/components/TaskShelfModal';
import buttonStyles from '@/style/button.less';
import { getJobPreserveKeyByShop, usePreserveParams } from '@/pages/Schedule/components/util';
import SelectedTargetFormItem from './SelectedTargetFormItem';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { tkshopSystemStatusGet } from '@/services/api-TKShopAPI/TkshopSystemController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import HelpLink from '@/components/HelpLink';
import FlowReadmeAsidePanel, {
  FlowReadmeAsidePanelWidth,
} from '@/components/Common/MarkdownView/FlowReadmeAsidePanel';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import { StyledRadioGroup } from '@/style/styled';

const ShopGroupConfirmModal = (props: {
  list: API.TkshopShopBuyerInfo[];
  count: number;
  onSubmit: (shop: API.ShopDetailVo) => void;
}) => {
  const [open, setOpen] = useState(true);
  const { list, onSubmit, count } = props;

  const [value, setValue] = useState(() => {
    return list[0].shopId!;
  });
  const { run: submit, loading } = useRequest(
    async () => {
      const shop = await shopByShopIdGet({
        shopId: value,
      }).then((res) => {
        return res.data!;
      });
      onSubmit(shop!);
      setOpen(false);
    },
    {
      manual: true,
    },
  );

  return (
    <DMModal
      headless
      width={640}
      onOk={submit}
      confirmLoading={loading}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <div style={{ marginBottom: 16 }}>
        <Typography.Text type={'secondary'}>
          {I18N.t(
            `您当前选中的${count?.toLocaleString()}个买家，分别在${
              list.length
            }个店铺产生过订单，您当前只能通过一个店铺发送私信：`,
          )}
        </Typography.Text>
      </div>

      <div style={{ paddingTop: 16 }}>
        <StyledRadioGroup
          value={value}
          onChange={(e) => {
            setValue(e.target.value);
          }}
        >
          <Row gutter={[8, 8]} wrap>
            {list.map(({ shopId, buyerIds }) => {
              return (
                <Col span={12} key={shopId}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
                    <Radio value={shopId}>
                      <ShopNodeById style={{ cursor: 'pointer' }} id={shopId} />
                    </Radio>
                    <Typography.Text type={'secondary'} ellipsis>
                      {I18N.t('共{{count}}个买家', { count: buyerIds?.length?.toLocaleString() })}
                    </Typography.Text>
                  </div>
                </Col>
              );
            })}
          </Row>
        </StyledRadioGroup>
      </div>
    </DMModal>
  );
};

const SendMsgToBuyerSelectedModal = (
  props: GhostModalWrapperComponentProps & {
    buyers: API.TkshopBuyerDto[];
    shop: API.ShopDetailVo;
    taskType?: 'TS_VideoADCode' | 'TS_SendBuyerIMChat';
    getExtraSettings?: (buyer?: API.TkshopBuyerDto) => Record<string, any>;
  },
) => {
  const { shop, buyers, taskType = 'TS_SendBuyerIMChat', getExtraSettings, modalProps } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const add_to_task = useRef(false);
  const { read, write } = usePreserveParams(
    getJobPreserveKeyByShop(
      taskType === 'TS_VideoADCode' ? 'TS_VideoADCode_hack_buyer' : taskType,
      shop?.id,
    ),
  );
  const { play } = useTaskPoolAddAnimation();
  const { run: add } = useAddTask();
  const { data: quotaInfo, run } = useRequest(
    async (shopId) => {
      const _quota = await tkshopSystemStatusGet().then((res) => {
        return res.data?.tkshopTeam?.buyerChatQuota || 0;
      });
      if (_quota > 0) {
        const used = await tkshopBuyerCountTodayInteractionsGet({
          shopId,
          interactType: 'im_chat',
        }).then((res) => {
          return res.data!;
        });
        const left = Math.max(_quota - used, 0);
        if (left === 0 || buyers.length > left) {
          return {
            data: {
              error: true,
              message: I18N.t(`超过可私信数量最大配额（今天可私信${_quota}人，还可私信${left}人）`),
            },
          };
        }
        return {
          data: {
            error: false,
            message: I18N.t(`今天可私信${_quota}人，还可私信${left}人`),
          },
        };
      }
      return {
        data: {
          error: false,
        },
      };
    },
    {
      manual: true,
    },
  );
  const hasAuth = useAuthJudgeCallback();
  const { run: submit, loading } = useRequest(
    async (e: any) => {
      if (!quotaInfo?.error) {
        add_to_task.current = false;
        const _values = trimValues(await form.validateFields());
        if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
          showFunctionCodeAlert();
        } else {
          const { device, ...rest } = _values;
          const extraSettings = getExtraSettings?.() || {};
          await tkshopJobsSendBuyerImChatPost({
            advanceSettings: { ...rest, taskType },
            wordsIds: getWordIdsFromSpeechConfig(rest.SpeechConfig),
            deviceId: device.deviceId,
            ghCreatorIds: buyers.map((item) => {
              return item.id!;
            }),
            ...extraSettings,
            content: getContentFromSpeechConfig(rest.SpeechConfig),
            shopId: shop.id!,
          });
          write(rest);
          play(e, 'TS_SendBuyerIMChat');
          setVisible(false);
        }
      }
    },
    {
      manual: true,
    },
  );
  const { run: addToTask, loading: adding } = useRequest(
    async (e: any) => {
      if (!quotaInfo?.error) {
        add_to_task.current = true;
        const _values = await form.validateFields();
        const { device, ...rest } = trimValues(_values);
        const items: API.AddTaskDrawerItem[] = [];
        buyers.forEach((buyer) => {
          const extraSettings = getExtraSettings?.(buyer) || {};
          items.push({
            taskType: 'TS_SendBuyerIMChat',
            rpaType: 'Browser',
            accountId: shop.id,
            creatorId: buyer.id,
            parameter: JSON.stringify({
              advanceSettings: { ...rest, ...extraSettings, taskType },
              wordsIds: getWordIdsFromSpeechConfig(rest.SpeechConfig),
              content: getContentFromSpeechConfig(rest.SpeechConfig),
            }),
          });
        });
        await add(
          {
            items,
          },
          e,
        );
        write(rest);
        setVisible(false);
      }
    },
    {
      manual: true,
    },
  );
  const footer = useMemo(() => {
    return (
      <div
        style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {quotaInfo?.message ? (
          <Space size={12}>
            <Space size={4}>
              <IconFontIcon iconName={'info_24'} />
              <Typography.Text type={quotaInfo?.error ? 'danger' : 'secondary'}>
                {quotaInfo?.message}
              </Typography.Text>
            </Space>
            <HelpLink href={'/tkshop2/brief#diff'} />
          </Space>
        ) : (
          <span />
        )}
        <Space>
          <Button
            onClick={addToTask}
            disabled={!!quotaInfo?.error}
            loading={adding}
            className={buttonStyles.successBtn}
          >
            {I18N.t('放到任务抽屉')}
          </Button>
          <Button onClick={submit} disabled={!!quotaInfo?.error} loading={loading} type={'primary'}>
            {I18N.t('立即执行')}
          </Button>
          <Button
            type={'default'}
            onClick={() => {
              setVisible(false);
            }}
          >
            {I18N.t('取消')}
          </Button>
        </Space>
      </div>
    );
  }, [addToTask, adding, loading, quotaInfo?.error, quotaInfo?.message, submit]);
  useEffect(() => {
    if (shop?.id) {
      run(shop.id);
    }
  }, [run, shop?.id]);
  useEffect(() => {
    if (shop.id!) {
      form.resetFields();
      read().then((res) => {
        if (res) {
          form.resetFields();
          form.setFieldsValue(res);
        }
      });
    }
  }, [form, read, shop.id]);
  return (
    <DMModal
      title={I18N.t('店铺私信')}
      bodyStyle={{ paddingBottom: 0 }}
      footer={footer}
      width={840 + FlowReadmeAsidePanelWidth}
      asideWrapperStyle={{
        flex: `0 0 ${FlowReadmeAsidePanelWidth}px`,
        height: 671,
      }}
      asidePanel={<FlowReadmeAsidePanel bizCode={'tkshop.TS_SendBuyerIMChat'} />}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <Form
        requiredMark={false}
        form={form}
        style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
      >
        <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 130 }}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <DMFormItem label={I18N.t('店铺')}>
                <ShopDetailNode data={shop} />
              </DMFormItem>
            </Col>
            <Col span={12}>
              <DMFormItem
                label="运行设备"
                name={'device'}
                rules={[
                  {
                    validator(_rule, value) {
                      if (value || add_to_task.current) {
                        return Promise.resolve();
                      }
                      return Promise.reject(I18N.t('请指定运行设备'));
                    },
                  },
                ]}
              >
                <SelectDeviceField />
              </DMFormItem>
            </Col>
          </Row>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <SelectedTargetFormItem
                showSettings
                label={I18N.t('指定买家')}
                style={{ marginBottom: 12 }}
                data={buyers}
              />
            </Col>
            <Col span={12}>
              <DMFormItem label={I18N.t('任务类型')} style={{ marginBottom: 12 }}>
                <Space>
                  <ColoursIcon className={TaskShelfJobIcon.TS_SendBuyerIMChat} />
                  <span>{I18N.t('发送站内消息')}</span>
                </Space>
              </DMFormItem>
            </Col>
          </Row>

          <SpeechConfigFields
            scene={taskType === 'TS_VideoADCode' ? 'ShopCreator' : 'ShopBuyer'}
            showCommentType
            speechType={taskType === 'TS_VideoADCode' ? 'RequestAdCode' : 'SendMsg'}
          />
          <DMFormItem
            label="图片或视频"
            name="medias"
            initialValue={[]}
            tooltip="可将图片或视频发送给买家，需要注意的是：只允许发送位于花漾云盘中的文件（可在花漾客户端中将图片文件上传至云盘），允许发送多张图片或视频（未回复的买家只允许接收5条消息）"
          >
            <SelectYunPanFileField
              mode={'multiple'}
              validate={(str) => {
                const reg = /\.(jfif|pjpeg|jpeg|pjp|jpg|png|mov|m4v|mp4)$/i;
                if (reg.test(str)) {
                  return;
                } else {
                  return '只支持选择图片(jfif|pjpeg|jpeg|pjp|jpg|png)或视频(mov|m4v|mp4)文件';
                }
              }}
            />
          </DMFormItem>
          <ProductInputTextArea shop={shop} />
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};
export default SendMsgToBuyerSelectedModal;
export function useSendMsgToBuyerSelectedModal() {
  return useRequest(
    async (selected: API.TkshopBuyerDto[]) => {
      const list = await tkshopBuyerCalcBuyerShopsPost({
        ids: selected.map((item) => {
          return item.id!;
        }),
      }).then((res) => {
        const { shopBuyersList } = res.data!;
        return shopBuyersList || [];
      });
      if (list.length === 1) {
        const shopId = list[0].shopId!;
        const shop = await shopByShopIdGet({
          shopId,
        }).then((res) => {
          return res.data!;
        });
        GhostModalCaller(<SendMsgToBuyerSelectedModal shop={shop} buyers={selected} />);
      } else {
        GhostModalCaller(
          <ShopGroupConfirmModal
            count={selected.length}
            list={list}
            onSubmit={(shop) => {
              const buyerIds =
                list!.find((item) => {
                  return item.shopId === shop.id;
                }).buyerIds || [];
              const buyers = selected.filter((item) => {
                return buyerIds?.includes(item.id!);
              });
              GhostModalCaller(<SendMsgToBuyerSelectedModal shop={shop} buyers={buyers} />);
            }}
          />,
          'ShopGroupConfirmModal',
        );
      }
    },
    {
      manual: true,
    },
  );
}
