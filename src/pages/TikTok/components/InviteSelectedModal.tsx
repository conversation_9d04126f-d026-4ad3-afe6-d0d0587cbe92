import type { CSSProperties } from 'react';
import { useEffect, useMemo, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Button, Col, Collapse, Form, InputNumber, Row, Space, Typography } from 'antd';
import { StyledCollapse } from '@/style/styled';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import ContactInfo from '@/pages/RpaFlows/components/forms/ContactInfo';
import SelectedTargetFormItem from '@/pages/TikTok/components/SelectedTargetFormItem';
import { useRequest } from '@@/plugin-request/request';
import { trimValues } from '@/utils/utils';
import { tkshopJobsSendTargetPlanByHandlePost } from '@/services/api-TKShopAPI/TkShopJobController';
import { getWordIdsFromSpeechConfig } from '@/pages/Setting/components/SendInviteFormFields';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import { autoOpenErrorField, useTaskPoolAddAnimation } from '@/hooks/interactions';
import moment from 'moment';
import { transformCreatorsForJob } from '@/pages/Creator/utils';
import { replaceDateInTemplate } from '@/pages/TikTok/components/InviteByFilterModal';
import ShopFormField from '@/pages/RpaFlows/components/items/ShopFormField';
import ColoursIcon from '@/components/Common/ColoursIcon';
import TkAreaConstants from '@/constants/TkAreaConstants';
import {
  PlanExpireTimeField,
  PlanNameFormItem,
  ProductsCommissionFields,
} from '@/pages/Schedule/components/SendInviteFields';
import { getJobPreserveKeyByShop, usePreserveParams } from '@/pages/Schedule/components/util';
import { TaskShelfJobIcon } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { tkshopSystemStatusGet } from '@/services/api-TKShopAPI/TkshopSystemController';
import { tkshopCountTodayInteractionsGet } from '@/services/api-TKShopAPI/TkshopInteractionController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import HelpLink from '@/components/HelpLink';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import FlowReadmeAsidePanel, {
  FlowReadmeAsidePanelWidth,
} from '@/components/Common/MarkdownView/FlowReadmeAsidePanel';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import AutoTagFormItems from '@/pages/TikTok/components/AutoTagFormItems';

export const InviteByHandleFields = (props: {
  shop: API.ShopBriefVo;
  refer: 'task' | 'schedule';
  style?: CSSProperties;
}) => {
  const { shop, refer = 'task', style, ...rest } = props;
  return (
    <div
      style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', ...style }}
      {...rest}
    >
      <div style={{ paddingLeft: 16, paddingRight: 16 }}>
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <PlanNameFormItem />
          </Col>
          <Col span={12}>
            <Form.Item noStyle shouldUpdate>
              {() => {
                if (refer === 'schedule') {
                  return (
                    <DMFormItem
                      label={I18N.t('有效期')}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                      initialValue={30}
                      name={'validDays'}
                    >
                      <InputNumber min={1} addonAfter={I18N.t('天')} />
                    </DMFormItem>
                  );
                }
                return (
                  <DMFormItem
                    label={I18N.t('过期时间')}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    name={'endDate'}
                  >
                    <PlanExpireTimeField />
                  </DMFormItem>
                );
              }}
            </Form.Item>
          </Col>
        </Row>
      </div>
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <StyledCollapse
          collapsible={shop ? 'header' : 'disabled'}
          defaultActiveKey={'intro'}
          ghost
          accordion
        >
          <Collapse.Panel header={I18N.t('商品佣金与内容偏好')} key="intro" forceRender>
            <ProductsCommissionFields shop={shop} />
          </Collapse.Panel>
          <Collapse.Panel header={I18N.t('联系方式与话术')} key="contact" forceRender>
            <ContactInfo showCommentType={false} shopVo={shop!} />
          </Collapse.Panel>
          <Collapse.Panel forceRender key={'tags'} header={'达人存储设置'}>
            <AutoTagFormItems type={'target_plan'} />
          </Collapse.Panel>
        </StyledCollapse>
      </div>
    </div>
  );
};

const InviteSelectedModal = (
  props: GhostModalWrapperComponentProps & {
    selected: API.TkshopCreatorDetailVo[];
    region?: string;
    refer?: 'list' | 'global';
  },
) => {
  const { modalProps } = props;
  const { selected, region: _region, refer = 'list' } = props;
  const [visible, setVisible] = useState(true);
  const { play } = useTaskPoolAddAnimation();
  const [shop, setShop] = useState<API.ShopDetailVo>();
  const hasAuth = useAuthJudgeCallback();
  const { read, write } = usePreserveParams(
    getJobPreserveKeyByShop('TS_TargetPlanByHandle', shop?.id),
  );
  const region = useMemo(() => {
    if (_region) {
      return _region;
    }
    return selected[0].region!;
  }, [_region, selected]);
  const [form] = Form.useForm();
  const { data: quotaInfo, run } = useRequest(
    async (shopId) => {
      const _quota = await tkshopSystemStatusGet().then((res) => {
        return res.data?.tkshopTeam?.targetPlanQuota!;
      });
      if (_quota > 0) {
        const used = await tkshopCountTodayInteractionsGet({
          shopId,
          interactType: 'target_plan',
        }).then((res) => {
          return res.data!;
        });
        const left = Math.max(_quota - used, 0);
        if (left === 0 || selected.length > left) {
          return {
            data: {
              error: true,
              message: I18N.t(`超过可邀约数量最大配额（今天可邀约${_quota}人，还可邀约${left}人）`),
            },
          };
        }
        return {
          data: {
            error: false,
            message: I18N.t(`今天可邀约${_quota}人，还可邀约${left}人`),
          },
        };
      }
      return {
        data: {
          error: false,
        },
      };
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (shop?.id) {
      run(shop.id);
    }
  }, [run, shop?.id]);
  const { run: submit, loading } = useRequest(
    async (e) => {
      if (!quotaInfo?.error) {
        const _values = trimValues(await form.validateFields().catch(autoOpenErrorField));
        if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
          showFunctionCodeAlert();
        } else {
          const { device, shop: _shop, ...rest } = _values;
          const advanceSettings = {
            ...rest,
            creators: transformCreatorsForJob(selected, refer),
            planName: replaceDateInTemplate(rest.planName),
          };
          if (!advanceSettings.bind_manual_tag) {
            delete advanceSettings.manual_tag;
          }
          await tkshopJobsSendTargetPlanByHandlePost({
            advanceSettings,
            wordsIds: getWordIdsFromSpeechConfig(rest.SpeechConfig),
            deviceId: device.deviceId,
            shopId: _shop.id!,
          });
          write(rest);
          setVisible(false);
          play(e, 'TS_TargetPlanByHandle');
        }
      }
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (shop?.id) {
      form.resetFields();
      read().then((res) => {
        let json = {
          endDate: moment().add(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
        };
        if (res) {
          json = res;
          let endDate = moment(json?.endDate);
          if (!endDate.isValid()) {
            endDate = moment().add(1, 'months');
          } else if (moment().diff(endDate, 'days') >= 0) {
            endDate = moment().add(1, 'months');
          }
          json.endDate = endDate.format('YYYY-MM-DD HH:mm:ss');
        }
        form.resetFields();
        form.setFieldsValue(json);
      });
    }
  }, [form, read, shop?.id]);
  const footer = useMemo(() => {
    return (
      <div
        style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {quotaInfo?.message ? (
          <Space size={12}>
            <Space size={4}>
              <IconFontIcon iconName={'info_24'} />
              <Typography.Text type={quotaInfo?.error ? 'danger' : 'secondary'}>
                {quotaInfo?.message}
              </Typography.Text>
            </Space>
            <HelpLink href={'/tkshop2/brief#diff'} />
          </Space>
        ) : (
          <span />
        )}
        <Space>
          <Button type={'primary'} disabled={!!quotaInfo?.error} onClick={submit} loading={loading}>
            {I18N.t('确定')}
          </Button>
          <Button
            type={'default'}
            onClick={() => {
              setVisible(false);
            }}
          >
            {I18N.t('取消')}
          </Button>
        </Space>
      </div>
    );
  }, [quotaInfo, loading, submit]);
  return (
    <DMModal
      title={I18N.t('定向邀约')}
      width={880 + FlowReadmeAsidePanelWidth}
      open={visible}
      asidePanel={<FlowReadmeAsidePanel bizCode={'tkshop.TS_TargetPlanByHandle'} />}
      asideWrapperStyle={{
        flex: `0 0 ${FlowReadmeAsidePanelWidth}px`,
        height: 631,
      }}
      footer={footer}
      bodyStyle={{ paddingBottom: 0 }}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <Form
        requiredMark={false}
        form={form}
        style={{ height: 500, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
      >
        <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 105 }}>
          <div style={{ paddingLeft: 16, paddingRight: 16 }}>
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <DMFormItem
                  label={I18N.t('店铺')}
                  name={'shop'}
                  initialValue={shop}
                  rules={[
                    {
                      required: true,
                      message: I18N.t('请选择店铺'),
                    },
                  ]}
                >
                  <ShopFormField onChange={setShop} area={TkAreaConstants.areas[region!]} />
                </DMFormItem>
              </Col>
              <Col span={12}>
                <DMFormItem
                  label="运行设备"
                  name={'device'}
                  rules={[
                    {
                      required: true,
                      message: I18N.t('请选择运行设备'),
                    },
                  ]}
                >
                  <SelectDeviceField />
                </DMFormItem>
              </Col>
            </Row>
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <SelectedTargetFormItem style={{ marginBottom: 12 }} data={selected} />
              </Col>
              <Col span={12}>
                <DMFormItem label={I18N.t('任务类型')} style={{ marginBottom: 12 }}>
                  <Space>
                    <ColoursIcon className={TaskShelfJobIcon.TS_TargetPlanByHandle} />
                    <span>{I18N.t('定向邀约')}</span>
                  </Space>
                </DMFormItem>
              </Col>
            </Row>
          </div>
          <InviteByHandleFields style={{ flex: 1, overflow: 'hidden' }} shop={shop} />
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};
export default InviteSelectedModal;
