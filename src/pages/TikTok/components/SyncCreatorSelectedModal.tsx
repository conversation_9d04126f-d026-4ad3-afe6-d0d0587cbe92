import { useCallback, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Button, Checkbox, Col, Form, Row, Select, Space, Typography } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import ShopSelector from '@/components/Common/Selector/ShopSelector';
import SelectedTargetFormItem from '@/pages/TikTok/components/SelectedTargetFormItem';
import { useRequest } from '@@/plugin-request/request';
import TkAreaConstants from '@/constants/TkAreaConstants';
import {
  getTaskShelfJobIcon,
  TaskShelfJobType,
  useAddTask,
} from '@/pages/TikTok/Live/components/TaskShelfModal';
import { trimValues } from '@/utils/utils';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import { useTaskPoolAddAnimation } from '@/hooks/interactions';
import { tkshopJobsBatchSyncCreatorsPost } from '@/services/api-TKShopAPI/TkShopJobController';
import { ProFormText } from '@ant-design/pro-form';
import type _ from 'lodash';
import CountryIcon from '@/components/Common/CountryIcon';
import buttonStyles from '@/style/button.less';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import { SyncCreatorFetchContactFormItem } from '@/pages/Schedule/components/util';
import DMConfirm from '@/components/Common/DMConfirm';
import FlowReadmeAsidePanel, {
  FlowReadmeAsidePanelWidth,
} from '@/components/Common/MarkdownView/FlowReadmeAsidePanel';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';

type FetchContactValue = 'default' | 'ignore' | 'force';

const SyncCreatorsByGroupModal = (
  props: GhostModalWrapperComponentProps & {
    group: _.Dictionary<API.TkshopCreatorDetailVo[]>;
    fetch_contact?: FetchContactValue;
  },
) => {
  const { group, fetch_contact = 'default', modalProps } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { play } = useTaskPoolAddAnimation();
  const { run: add } = useAddTask();
  const add_to_task = useRef(false);
  const hasAuth = useAuthJudgeCallback();

  const { run: submit, loading } = useRequest(
    async (e) => {
      add_to_task.current = false;
      const { device, _regions, ...values } = trimValues(await form.validateFields());
      if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
        showFunctionCodeAlert();
      } else {
        const entries = Object.entries(values);
        for (let i = 0; i < entries.length; i++) {
          const [region, value] = entries[i];
          const { enabled, fetch_contact, shopId, creators } = value;
          const shopCreators = {};
          if (enabled) {
            shopCreators[shopId] = {
              ghCreatorIds: creators.map((item) => item.id!),
              advanceSettings: {
                fetch_contact,
              },
            };
            await tkshopJobsBatchSyncCreatorsPost({
              shopCreators,
              deviceId: device.deviceId!,
            });
            play(e, 'TS_SyncCreator');
          }
        }
        setOpen(false);
      }
    },
    {
      manual: true,
    },
  );
  const { run: addToTask, loading: adding } = useRequest(
    async (e: any) => {
      add_to_task.current = true;
      const { device, _regions, ...values } = trimValues(await form.validateFields());
      const items: API.AddTaskDrawerItem[] = [];
      const entries = Object.entries(values);
      entries.forEach(([region, value]) => {
        const { enabled, fetch_contact, shopId, creators } = value;
        if (enabled) {
          creators.forEach((item) => {
            items.push({
              rpaType: 'Browser',
              accountId: shopId!,
              taskType: 'TS_SyncCreator',
              creatorId: item.id!,
              parameter: JSON.stringify({
                fetch_contact,
              }),
            });
          });
        }
      });
      await add(
        {
          items,
        },
        e,
      );
      setOpen(false);
    },
    {
      manual: true,
    },
  );
  const footer = useMemo(() => {
    return (
      <Space>
        <Button onClick={addToTask} loading={adding} className={buttonStyles.successBtn}>
          {I18N.t('放到任务抽屉')}
        </Button>
        <Button onClick={submit} loading={loading} type={'primary'}>
          {I18N.t('立即执行')}
        </Button>
        <Button
          type={'default'}
          onClick={() => {
            setOpen(false);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
  }, [addToTask, adding, loading, submit]);
  const list = useMemo(() => {
    const _nodes: any[] = [];
    Object.entries(group).forEach(([region, creators]) => {
      _nodes.push(
        <div key={region}>
          <ProFormText
            hidden
            name={region}
            initialValue={{ enabled: true, creators, fetch_contact }}
          />
          <Form.Item shouldUpdate noStyle>
            {(f) => {
              const checked = f.getFieldValue([region, 'enabled']);
              const area = TkAreaConstants.areas[region];
              const label = TkAreaConstants.areaLabels[region];

              return (
                <div>
                  <Form.Item valuePropName={'checked'} name={[region, 'enabled']}>
                    <Checkbox>
                      <Space>
                        <CountryIcon size={16} country={area} />
                        <span>{label}</span>
                        <Typography.Text style={{ color: '#999' }}>
                          （共选中 {creators?.length?.toLocaleString()} 个达人）
                        </Typography.Text>
                      </Space>
                    </Checkbox>
                  </Form.Item>

                  <Row gutter={[16, 16]} style={{ paddingLeft: 24 }}>
                    <Col span={12}>
                      <DMFormItem
                        label={'店铺'}
                        style={{ marginBottom: 0 }}
                        name={[region, 'shopId']}
                        rules={[
                          {
                            required: checked,
                            message: I18N.t(`请选择店铺`),
                          },
                        ]}
                      >
                        <ShopSelector
                          area={area}
                          autoInit
                          placeholder={I18N.t('请选择')}
                          showPlaceholderOption={false}
                          disabled={!checked}
                        />
                      </DMFormItem>
                    </Col>
                    <Col span={12}>
                      <DMFormItem
                        label={'联系方式'}
                        style={{ marginBottom: 0 }}
                        name={[region, 'fetch_contact']}
                        rules={[{ required: checked, message: I18N.t(`联系方式更新策略`) }]}
                      >
                        <Select
                          dropdownMatchSelectWidth={false}
                          options={[
                            {
                              label: I18N.t('所有联系方式都为空时才更新'),
                              value: 'default',
                            },
                            {
                              label: I18N.t('强制更新'),
                              value: 'force',
                            },
                            {
                              label: I18N.t('不更新'),
                              value: 'ignore',
                            },
                          ]}
                        />
                      </DMFormItem>
                    </Col>
                  </Row>
                </div>
              );
            }}
          </Form.Item>
        </div>,
      );
    });
    return _nodes;
  }, [fetch_contact, group]);
  const size = useMemo(() => {
    let count = 0;
    Object.values(group).forEach((creators) => {
      count += creators.length;
    });
    return count;
  }, [group]);
  return (
    <DMModal
      width={640}
      headless
      footer={footer}
      bodyStyle={{
        paddingBottom: 0,
      }}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      {...modalProps}
    >
      <div>
        <div
          style={{
            marginBottom: 16,
          }}
        >
          <Typography.Text type={'secondary'}>
            {I18N.t(`对选中的 ${size?.toLocaleString()} 个达人进行基础信息更新：`)}
          </Typography.Text>
        </div>
        <Form form={form} requiredMark={false}>
          <DMFormItemContext.Provider value={{ labelWidth: 80 }}>
            <Form.Item
              name={'_regions'}
              rules={[
                {
                  validator() {
                    const { _regions, device, ...values } = form.getFieldsValue();
                    if (Object.values(values).some(({ enabled }) => enabled)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(I18N.t('请选择要更新的达人')));
                  },
                },
              ]}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>{list}</div>
            </Form.Item>
            <DMFormItem
              style={{ paddingLeft: 24 }}
              label={I18N.t('运行设备')}
              name={'device'}
              rules={[
                {
                  validator(_rule, value) {
                    if (value || add_to_task.current) {
                      return Promise.resolve();
                    }
                    return Promise.reject(I18N.t('请指定运行设备'));
                  },
                },
              ]}
            >
              <SelectDeviceField />
            </DMFormItem>
          </DMFormItemContext.Provider>
        </Form>
      </div>
    </DMModal>
  );
};

const SyncCreatorSelectedModal = (
  props: GhostModalWrapperComponentProps & {
    selected: API.TkshopCreatorDetailVo[];
    region?: string;
    fetch_contact?: FetchContactValue;
    refer?: 'list' | 'detail';
  },
) => {
  const {
    selected,
    fetch_contact = 'default',
    refer = 'detail',
    region: _region,
    modalProps,
  } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const { play } = useTaskPoolAddAnimation();
  const region = useMemo(() => {
    if (_region) {
      return _region;
    }
    return selected[0].region!;
  }, [_region, selected]);
  const add_to_task = useRef(false);
  const { run: add } = useAddTask();
  const hasAuth = useAuthJudgeCallback();
  const { run: addToTask, loading: adding } = useRequest(
    async (e: any) => {
      add_to_task.current = true;
      const { shopId, ...rest } = await form.validateFields();
      await add(
        {
          items: selected.map((item) => {
            return {
              rpaType: 'Browser',
              accountId: shopId!,
              taskType: 'TS_SyncCreator',
              creatorId: item.id!,
              parameter: JSON.stringify(trimValues(rest)),
            };
          }),
        },
        e,
      ).then(() => {
        setVisible(false);
      });
    },
    {
      manual: true,
    },
  );
  const { run: submit, loading } = useRequest(
    async (e: any) => {
      add_to_task.current = false;
      const { device, shopId, fetch_contact } = trimValues(await form.validateFields());
      if (!hasAuth([Functions.RPA_LIST, Functions.RPA_RUN])) {
        showFunctionCodeAlert();
      } else {
        await tkshopJobsBatchSyncCreatorsPost({
          shopCreators: {
            [shopId]: {
              ghCreatorIds: selected.map((item) => {
                return item.id!;
              }),
              advanceSettings: {
                fetch_contact,
              },
            },
          },
          deviceId: device.deviceId!,
        });
        play(e, 'TS_SyncCreator');
        setVisible(false);
      }
    },
    {
      manual: true,
    },
  );
  const footer = useMemo(() => {
    if (refer === 'detail') {
      return (
        <Space>
          <Button onClick={submit} loading={loading}>
            {I18N.t('立即执行')}
          </Button>
          <Button onClick={addToTask} loading={adding} type={'primary'}>
            {I18N.t('放到任务抽屉')}
          </Button>
          <Button
            type={'default'}
            onClick={() => {
              setVisible(false);
            }}
          >
            {I18N.t('取消')}
          </Button>
        </Space>
      );
    }
    return (
      <Space>
        <Button onClick={addToTask} loading={adding} className={buttonStyles.successBtn}>
          {I18N.t('放到任务抽屉')}
        </Button>
        <Button onClick={submit} loading={loading} type={'primary'}>
          {I18N.t('立即执行')}
        </Button>
        <Button
          type={'default'}
          onClick={() => {
            setVisible(false);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
  }, [addToTask, adding, loading, refer, submit]);

  return (
    <DMModal
      title={I18N.t('达人信息更新')}
      bodyStyle={{ paddingBottom: 0 }}
      width={640 + FlowReadmeAsidePanelWidth}
      footer={footer}
      open={visible}
      asideWrapperStyle={{
        flex: `0 0 ${FlowReadmeAsidePanelWidth}px`,
        height: 491,
      }}
      asidePanel={<FlowReadmeAsidePanel bizCode={'tkshop.TS_SyncCreator'} />}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 100 }}>
        <Form
          requiredMark={false}
          form={form}
          style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
        >
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <SelectedTargetFormItem data={selected} />
            <DMFormItem label={I18N.t('任务类型')}>
              <Space align={'center'} size={4}>
                <Typography.Text>{getTaskShelfJobIcon('TS_SyncCreator')}</Typography.Text>
                <span>{TaskShelfJobType.TS_SyncCreator}</span>
              </Space>
            </DMFormItem>
            <SyncCreatorFetchContactFormItem initialValue={fetch_contact} />
            <DMFormItem
              label={I18N.t('店铺')}
              name={'shopId'}
              rules={[{ required: true, message: I18N.t('请选择店铺') }]}
            >
              <ShopSelector
                autoInit
                area={TkAreaConstants.areas[region!]}
                showPlaceholderOption={false}
                placeholder={I18N.t('请选择')}
              />
            </DMFormItem>
            <DMFormItem
              label={I18N.t('运行设备')}
              name={'device'}
              rules={[
                {
                  validator(_rule, value) {
                    if (value || add_to_task.current) {
                      return Promise.resolve();
                    }
                    return Promise.reject(I18N.t('请指定运行设备'));
                  },
                },
              ]}
            >
              <SelectDeviceField />
            </DMFormItem>
          </div>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
export default SyncCreatorSelectedModal;

type Props = {
  group: _.Dictionary<API.TkshopCreatorDetailVo[]>;
  fetch_contact?: FetchContactValue;
};
export function useSyncCreatorsModal() {
  return useCallback((args: Props) => {
    const { group, fetch_contact } = args;
    let count = 0;
    Object.values(group).forEach((creators) => {
      count += creators.length;
    });
    if (count === 0) {
      DMConfirm({
        title: I18N.t('达人没有所属国家'),
        type: 'info',
      });
      return;
    }
    const areas = Object.keys(group);
    if (areas.length === 1) {
      const area = areas[0];
      GhostModalCaller(
        <SyncCreatorSelectedModal
          refer={'list'}
          region={areas[0]}
          fetch_contact={fetch_contact}
          selected={group[area]}
        />,
      );
    } else {
      GhostModalCaller(<SyncCreatorsByGroupModal fetch_contact={fetch_contact} group={group} />);
    }
  }, []);
}
