import { createSlot } from '@/hooks/createSlot';
import type { FormItemProps } from 'antd';
import {
  Checkbox,
  Col,
  Form,
  InputNumber,
  Radio,
  Row,
  Space,
  Steps,
  Tooltip,
  Typography,
} from 'antd';
import DMFormItem from '@/components/Common/DMFormItem';
import I18N from '@/i18n';
import { useCallback, useState } from 'react';

import IconFontIcon from '@/components/Common/IconFontIcon';
import {
  rpaTaskDbGetByKeyGet,
  rpaTaskDbPutByKeyPut,
} from '@/services/api-RPAAPI/RpaTaskDbController';
import _ from 'lodash';
import {
  getContentFromSpeechConfig,
  getWordIdsFromSpeechConfig,
} from '@/pages/Setting/components/SendInviteFormFields';
import { InviteByFilterFields } from '@/pages/TikTok/components/InviteByFilterModal';
import { InviteByHandleFields } from '@/pages/TikTok/components/InviteSelectedModal';
import { SendMsgByFilterFields } from '@/pages/TikTok/components/SendMsgByFilterModal';
import { SendMsgByHandleFields } from '@/pages/TikTok/components/SendMsgToCreatorSelectedModal';
import EmptyView from '@/components/Common/EmptyView';
import HelpLink from '@/components/HelpLink';
import type { SelectorProps } from '@/components/Common/Selector';
import Selector from '@/components/Common/Selector';
import moment from 'moment';
import { DatePickerCon } from '@/pages/RpaFlows/components/items/DatePickerCon';
import { ShopDetailNode } from '@/components/Common/ShopNode';
import styled from 'styled-components';
import { DemandPaymentFields } from '@/pages/TikTok/components/DemandPaymentModal';
import DMModal from '@/components/Common/Modal/DMModal';
import { generateUniqueKey } from '@/utils/utils';
import HelpTooltip from '@/components/HelpTooltip';

export const ScheduleHeaderSlot = createSlot();

export function usePreserveParams(key: string) {
  const read = useCallback(async () => {
    const res = await rpaTaskDbGetByKeyGet({
      key: key,
      scope: 'team',
    });
    try {
      return JSON.parse(res.data!);
    } catch (e) {
      console.log(e);
      return null;
    }
  }, [key]);

  const write = useCallback(
    async (values: any) => {
      if (values) {
        let _params = values;
        if (_.isObject(values)) {
          _params = JSON.stringify(values);
        }
        await rpaTaskDbPutByKeyPut(
          {
            key,
            scope: 'team',
          },
          _params,
        );
      }
    },
    [key],
  );
  return {
    read,
    write,
  };
}

export type CreatorFilterType = 'ByFilter' | 'ByHandle';

export const SyncSampleRequestFormItem = (props: {
  initialValue?: {
    need_query_contents_in_days: boolean;
    query_contents_in_days: number;
    need_query_sample_request_creator: boolean;
    query_sample_request_creator_in_days: number;
  };
}) => {
  const {
    initialValue = {
      need_query_contents_in_days: true,
      query_contents_in_days: 30,
      need_query_sample_request_creator: true,
      query_sample_request_creator_in_days: 30,
    },
  } = props;

  return (
    <>
      <DMFormItem
        name="need_query_sample_request_creator"
        valuePropName="checked"
        initialValue={initialValue.need_query_sample_request_creator ?? true}
      >
        <Checkbox>自动更新符合下述条件的索样达人的基础信息</Checkbox>
      </DMFormItem>
      <DMFormItem shouldUpdate>
        {(f) => {
          const disabled = !f.getFieldValue('need_query_sample_request_creator');
          return (
            <Row align="middle" gutter={0}>
              <Col style={{ paddingLeft: 24 }}>基础信息更新时间早于</Col>
              <Col style={{ paddingLeft: 4 }}>
                <DMFormItem
                  name="query_sample_request_creator_in_days"
                  initialValue={initialValue.query_sample_request_creator_in_days ?? 30}
                  rules={[{ required: true, message: '请输入查询条件' }]}
                  noStyle
                >
                  <InputNumber min={1} max={365} disabled={disabled} />
                </DMFormItem>
              </Col>
              <Col style={{ paddingLeft: 4 }}>天的达人</Col>
            </Row>
          );
        }}
      </DMFormItem>
      <DMFormItem
        name="need_query_contents_in_days"
        valuePropName="checked"
        initialValue={initialValue.need_query_contents_in_days ?? true}
      >
        <Checkbox>{I18N.t('查询符合下述条件的、已完成的索样记录的视频/直播的产出详情')}</Checkbox>
      </DMFormItem>
      <DMFormItem shouldUpdate>
        {(f) => {
          const disabled = !f.getFieldValue('need_query_contents_in_days');
          return (
            <Row align="middle" gutter={0}>
              <Col style={{ paddingLeft: 24 }}>{I18N.t('查询最近')}：</Col>
              <Col>
                <DMFormItem
                  name="query_contents_in_days"
                  initialValue={initialValue.query_contents_in_days ?? 30}
                  rules={[{ required: true, message: I18N.t('请输入查询条件') }]}
                  noStyle
                >
                  <InputNumber min={1} max={365} disabled={disabled} />
                </DMFormItem>
              </Col>
              <Col style={{ paddingLeft: 4 }}>{I18N.t('天创建的索样记录')}</Col>
            </Row>
          );
        }}
      </DMFormItem>
    </>
  );
};

export const ShopInfoSyncActionsFormItem = (props: {
  label?: any;
  columns?: number;
  tooltip?: boolean;
  initialValue?: {
    need_sync_product: boolean;
    need_sync_order: boolean;
    need_sync_sample_apply: boolean;
    need_sync_creator: boolean;
    need_sync_target_plan: boolean;
    need_re_query_affiliate_order: boolean;
    re_query_affiliate_order_sum: number;
    need_query_contents_in_days: boolean;
    query_contents_in_days: number;
    need_buyer_contact_info: boolean;
    need_query_sample_request_creator: boolean;
    query_sample_request_creator_in_days: number;
    query_buyer_contact_info_sum: number;
  };
}) => {
  const {
    label = I18N.t('工作内容'),
    columns = 1,
    tooltip = I18N.t('建议所有同步选项全部开启'),
    initialValue = {
      need_sync_product: true,
      need_sync_order: true,
      need_sync_sample_apply: true,
      need_sync_creator: true,
      need_sync_target_plan: true,
      need_re_query_affiliate_order: true,
      re_query_affiliate_order_sum: 200,
      need_query_contents_in_days: true,
      query_contents_in_days: 30,
      need_buyer_contact_info: true,
      need_query_sample_request_creator: true,
      query_sample_request_creator_in_days: 30,
      query_buyer_contact_info_sum: 10,
    },
  } = props;
  const [orderModalVisible, setOrderModalVisible] = useState(false);
  const [sampleApplyModalVisible, setSampleApplyModalVisible] = useState(false);
  const span = 24 / columns;
  return (
    <>
      <DMFormItem
        validateFirst
        name={'TS_SyncShopInfo'}
        tooltip={tooltip}
        rules={[
          {
            validator(rule, val) {
              if (
                _.size(
                  _.omitBy(val, (value) => {
                    return !value;
                  }),
                ) === 0
              ) {
                return Promise.reject(
                  new Error(
                    I18N.t('请指定{{label}}', {
                      label,
                    }),
                  ),
                );
              }
              return Promise.resolve();
            },
          },
        ]}
        initialValue={initialValue}
        label={label}
      >
        <Form.Item shouldUpdate noStyle>
          {(f) => {
            const _value: string[] = [];
            Object.entries(f.getFieldValue('TS_SyncShopInfo')).forEach(([key, value]) => {
              if (value) {
                _value.push(key);
              }
            });
            return (
              <Checkbox.Group
                style={{ display: 'block', overflow: 'hidden' }}
                value={_value}
                onChange={(vals) => {
                  let re_query_affiliate_order_sum =
                    f.getFieldValue('re_query_affiliate_order_sum') ?? 200;
                  let query_buyer_contact_info_sum =
                    f.getFieldValue('query_buyer_contact_info_sum') ?? 10;
                  let query_contents_in_days = f.getFieldValue('query_contents_in_days') ?? 30;
                  let query_sample_request_creator_in_days =
                    f.getFieldValue('query_sample_request_creator_in_days') ?? 30;
                  const need_buyer_contact_info =
                    f.getFieldValue('need_buyer_contact_info') ?? true;
                  if (
                    !f.getFieldValue('need_re_query_affiliate_order') ||
                    !re_query_affiliate_order_sum
                  ) {
                    re_query_affiliate_order_sum = 0;
                  }
                  if (
                    !f.getFieldValue('need_buyer_contact_info') ||
                    !query_buyer_contact_info_sum
                  ) {
                    query_buyer_contact_info_sum = 0;
                  }
                  if (!f.getFieldValue('need_query_contents_in_days') || !query_contents_in_days) {
                    query_contents_in_days = 0;
                  }
                  if (
                    !f.getFieldValue('need_query_sample_request_creator') ||
                    !query_sample_request_creator_in_days
                  ) {
                    query_sample_request_creator_in_days = 0;
                  }
                  const shopInfoSyncConfig = {
                    re_query_affiliate_order_sum,
                    query_contents_in_days,
                    need_buyer_contact_info,
                    query_buyer_contact_info_sum,
                    query_sample_request_creator_in_days,
                  };
                  vals.forEach((key) => {
                    shopInfoSyncConfig[key] = true;
                  });
                  f.setFieldValue('TS_SyncShopInfo', shopInfoSyncConfig);
                }}
              >
                <Row gutter={[8, 8]} wrap>
                  <Col span={span}>
                    <Checkbox value={'need_sync_product'} style={{ lineHeight: '32px' }}>
                      <span>{I18N.t('商品同步')}</span>
                      <Tooltip
                        title={I18N.t(
                          '抓取并保存店铺所售商品信息，方便用户在执行达人邀约时选择商品',
                        )}
                      >
                        <Typography.Link style={{ marginLeft: 32 }}>
                          <IconFontIcon size={'inherit'} iconName={'bangzhu_24'} />
                        </Typography.Link>
                      </Tooltip>
                    </Checkbox>
                  </Col>
                  <Col span={span}>
                    <Checkbox value={'need_sync_order'} style={{ lineHeight: '32px' }}>
                      <span>{I18N.t('订单同步')}</span>
                      <a
                        style={{ marginLeft: 20 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          setOrderModalVisible(true);
                        }}
                      >
                        {I18N.t('高级')}
                      </a>
                      <Tooltip
                        title={I18N.t(
                          '抓取店铺所有订单（含买家信息），将买家保存至团队买家库，完成买家的私域管理',
                        )}
                      >
                        <Typography.Link style={{ marginLeft: 32 }}>
                          <IconFontIcon size={'inherit'} iconName={'bangzhu_24'} />
                        </Typography.Link>
                      </Tooltip>
                    </Checkbox>
                  </Col>
                  <Col span={span}>
                    <Checkbox value={'need_sync_sample_apply'} style={{ lineHeight: '32px' }}>
                      <span>{I18N.t('索样记录同步')}</span>
                      <a
                        style={{ marginLeft: 20 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          setSampleApplyModalVisible(true);
                        }}
                      >
                        {I18N.t('高级')}
                      </a>
                      <Tooltip
                        title={I18N.t(
                          '抓取店铺所有索样达人，保存至团队达人库并形成沟通记录，既方便用户在私域系统中进行索样审批，也完成达人的私域管理',
                        )}
                      >
                        <Typography.Link style={{ marginLeft: 32 }}>
                          <IconFontIcon size={'inherit'} iconName={'bangzhu_24'} />
                        </Typography.Link>
                      </Tooltip>
                    </Checkbox>
                  </Col>
                  <Col span={span}>
                    <Checkbox value={'need_sync_creator'} style={{ lineHeight: '32px' }}>
                      <span>{I18N.t('达人带货信息同步')}</span>
                      <Tooltip
                        title={I18N.t(
                          '抓取达人最近28天的带货信息，抓取后的数据保存至团队达人库，完成达人的私域管理',
                        )}
                      >
                        <Typography.Link style={{ marginLeft: 32 }}>
                          <IconFontIcon size={'inherit'} iconName={'bangzhu_24'} />
                        </Typography.Link>
                      </Tooltip>
                    </Checkbox>
                  </Col>
                  <Col span={span}>
                    <Checkbox value={'need_sync_target_plan'} style={{ lineHeight: '32px' }}>
                      <span>{I18N.t('达人定向邀约计划同步')}</span>
                      <Tooltip
                        title={I18N.t(
                          '抓取店铺所有的定向邀约计划，方便检查筛选后的达人是否能够被邀约',
                        )}
                      >
                        <Typography.Link style={{ marginLeft: 32 }}>
                          <IconFontIcon size={'inherit'} iconName={'bangzhu_24'} />
                        </Typography.Link>
                      </Tooltip>
                    </Checkbox>
                  </Col>
                </Row>
              </Checkbox.Group>
            );
          }}
        </Form.Item>
      </DMFormItem>
      <DMFormItem shouldUpdate noStyle>
        {(f) => {
          return (
            <DMModal
              title={I18N.t('高级设置')}
              open={orderModalVisible}
              forceRender
              onOk={() => {
                const v = f.getFieldValue('TS_SyncShopInfo');
                let re_query_affiliate_order_sum =
                  f.getFieldValue('re_query_affiliate_order_sum') ?? 200;
                let query_buyer_contact_info_sum =
                  f.getFieldValue('query_buyer_contact_info_sum') ?? 10;
                if (
                  !f.getFieldValue('need_re_query_affiliate_order') ||
                  !f.getFieldValue('re_query_affiliate_order_sum')
                ) {
                  re_query_affiliate_order_sum = 0;
                }
                if (
                  !f.getFieldValue('need_buyer_contact_info') ||
                  !f.getFieldValue('query_buyer_contact_info_sum')
                ) {
                  query_buyer_contact_info_sum = 0;
                }
                const need_buyer_contact_info = f.getFieldValue('need_buyer_contact_info') ?? true;
                f.setFieldValue('TS_SyncShopInfo', {
                  ...v,
                  re_query_affiliate_order_sum,
                  query_buyer_contact_info_sum,
                  need_buyer_contact_info: need_buyer_contact_info,
                });
                setOrderModalVisible(false);
              }}
              onCancel={() => {
                setOrderModalVisible(false);
              }}
            >
              <DMFormItem
                name="need_buyer_contact_info"
                valuePropName="checked"
                initialValue={initialValue.need_buyer_contact_info ?? true}
              >
                <Checkbox>
                  <span>{I18N.t('是否抓取买家的联系方式（如电话号码）')}</span>
                  <Tooltip
                    title={I18N.t(
                      '不同的店铺每天能够抓取到的买家的联系方式的数量是不同的，请根据您的店铺性质及运营状况决定是否抓取',
                    )}
                  >
                    <Typography.Link style={{ marginLeft: 32 }}>
                      <IconFontIcon size={'inherit'} iconName={'bangzhu_24'} />
                    </Typography.Link>
                  </Tooltip>
                </Checkbox>
              </DMFormItem>
              <DMFormItem shouldUpdate>
                {() => {
                  const disabled = !f.getFieldValue('need_buyer_contact_info');
                  return (
                    <Row align="middle" gutter={0}>
                      <Col style={{ paddingLeft: 24 }}>{I18N.t('每次抓取数量')}：</Col>
                      <Col>
                        <DMFormItem
                          name="query_buyer_contact_info_sum"
                          initialValue={initialValue.query_buyer_contact_info_sum ?? 10}
                          rules={[{ required: true, message: I18N.t('请输入抓取数量') }]}
                          noStyle
                        >
                          <InputNumber disabled={disabled} min={0} max={10000} />
                        </DMFormItem>
                      </Col>
                      <Col style={{ paddingLeft: 4 }}>{I18N.t('个买家')}</Col>
                    </Row>
                  );
                }}
              </DMFormItem>
              <DMFormItem
                name="need_re_query_affiliate_order"
                valuePropName="checked"
                initialValue={initialValue.need_re_query_affiliate_order ?? true}
              >
                <Checkbox>{I18N.t('对缺少带货来源的联盟订单进行二次查询')}</Checkbox>
              </DMFormItem>
              <DMFormItem shouldUpdate>
                {() => {
                  const disabled = !f.getFieldValue('need_re_query_affiliate_order');
                  return (
                    <Row align="middle" gutter={0}>
                      <Col style={{ paddingLeft: 24 }}>{I18N.t('每次查询数量')}：</Col>
                      <Col>
                        <DMFormItem
                          name="re_query_affiliate_order_sum"
                          initialValue={initialValue.re_query_affiliate_order_sum ?? 200}
                          rules={[{ required: true, message: I18N.t('请输入查询数量') }]}
                          noStyle
                        >
                          <InputNumber disabled={disabled} min={0} max={1000} />
                        </DMFormItem>
                      </Col>
                      <Col style={{ paddingLeft: 4 }}>{I18N.t('笔订单')}</Col>
                    </Row>
                  );
                }}
              </DMFormItem>
            </DMModal>
          );
        }}
      </DMFormItem>
      <DMFormItem shouldUpdate noStyle>
        {(f) => {
          return (
            <DMModal
              title={I18N.t('高级设置')}
              open={sampleApplyModalVisible}
              forceRender
              onOk={() => {
                const v = f.getFieldValue('TS_SyncShopInfo');
                let query_contents_in_days = f.getFieldValue('query_contents_in_days') ?? 30;
                let query_sample_request_creator_in_days =
                  f.getFieldValue('query_sample_request_creator_in_days') ?? 30;
                if (
                  !f.getFieldValue('need_query_contents_in_days') ||
                  !f.getFieldValue('query_contents_in_days')
                ) {
                  query_contents_in_days = 0;
                }
                if (
                  !f.getFieldValue('need_query_sample_request_creator') ||
                  !f.getFieldValue('query_sample_request_creator_in_days')
                ) {
                  query_sample_request_creator_in_days = 0;
                }
                f.setFieldValue('TS_SyncShopInfo', {
                  ...v,
                  query_contents_in_days,
                  query_sample_request_creator_in_days,
                });
                setSampleApplyModalVisible(false);
              }}
              onCancel={() => {
                setSampleApplyModalVisible(false);
              }}
            >
              <SyncSampleRequestFormItem />
            </DMModal>
          );
        }}
      </DMFormItem>
    </>
  );
};
export type SyncCreatorPolicy = 'default' | 'force' | 'ignore';
export const SyncCreatorFetchContactFormItem = (
  props: FormItemProps & {
    initialValue?: SyncCreatorPolicy;
  },
) => {
  const { initialValue = 'default', ...rest } = props;
  return (
    <DMFormItem
      name={'fetch_contact'}
      initialValue={initialValue}
      label={I18N.t('联系方式')}
      tooltip={
        <>
          <span>
            {I18N.t(
              'TK后台允许每日查询联系方式的次数是有限的，请您在认为必要的情况下再更新联系方式（仅对东南亚的达人有效）',
            )}
          </span>
          <HelpLink href="/tkshop2/faq#contact" style={{ marginLeft: 20 }} />
        </>
      }
      {...rest}
    >
      <Radio.Group>
        <Space direction={'vertical'}>
          <Radio value={'default'} style={{ lineHeight: '32px' }}>
            {I18N.t('只有当所有联系方式都为空时才会查询并更新联系方式')}
          </Radio>
          <Radio value={'force'} style={{ lineHeight: '32px' }}>
            {I18N.t('强制更新联系方式')}
          </Radio>
          <Radio value={'ignore'} style={{ lineHeight: '32px' }}>
            {I18N.t('不更新联系方式')}
          </Radio>
        </Space>
      </Radio.Group>
    </DMFormItem>
  );
};

// 清理定向邀约计划
export const TargetPlanClearFormItems = () => {
  return (
    <>
      <Form.Item shouldUpdate noStyle>
        {(f) => {
          const before_type = f.getFieldValue('before_type');
          return (
            <DMFormItem name="before_type" label={I18N.t('指定时间')} initialValue="before_days">
              <Radio.Group>
                <Space size={8} direction="vertical">
                  <Row align="middle">
                    <Col>
                      <Radio style={{ lineHeight: '32px' }} value="before_days" />
                    </Col>
                    <Col style={{ fontSize: 14, lineHeight: '32px' }}>
                      <Row gutter={8}>
                        <Col>
                          <DMFormItem name="before_days" initialValue={25} noStyle>
                            <InputNumber
                              disabled={before_type !== 'before_days'}
                              min={1}
                              max={100}
                            />
                          </DMFormItem>
                        </Col>
                        <Col>{I18N.t('天之前创建的定向邀约计划')}</Col>
                      </Row>
                    </Col>
                  </Row>
                  <Row align="middle">
                    <Col>
                      <Radio style={{ lineHeight: '32px' }} value="before_date" />
                    </Col>
                    <Col style={{ fontSize: 14, lineHeight: '32px' }}>
                      <Row gutter={8}>
                        <Col>
                          <DMFormItem
                            name="before_date"
                            noStyle
                            initialValue={moment().format('YYYY-MM-DD')}
                            rules={[
                              () => ({
                                validator: (rule, value) => {
                                  if (f.getFieldValue('before_type') !== 'before_date')
                                    return Promise.resolve(true);
                                  if (!value) {
                                    throw new Error(I18N.t('请选择日期'));
                                  }
                                  return Promise.resolve(true);
                                },
                              }),
                            ]}
                          >
                            <DatePickerCon disabled={before_type !== 'before_date'} />
                          </DMFormItem>
                        </Col>
                        <Col>{I18N.t('之前创建的定向邀约计划')}</Col>
                      </Row>
                    </Col>
                  </Row>
                </Space>
              </Radio.Group>
            </DMFormItem>
          );
        }}
      </Form.Item>
      <DMFormItem label={I18N.t('清理选项')} shouldUpdate>
        <Form.Item shouldUpdate noStyle>
          {(f) => {
            const disabled =
              !f.getFieldValue('clear_person_exist') && f.getFieldValue('clear_person_empty');
            return (
              <div style={{ display: 'flex', gap: 8, height: '32px', alignItems: 'center' }}>
                <Form.Item
                  name={'clear_person_empty'}
                  initialValue={true}
                  noStyle
                  valuePropName={'checked'}
                >
                  <Checkbox disabled={disabled} />
                </Form.Item>
                <span>
                  {I18N.t('指定时间之前创建的邀约计划，如果没有达人接受邀约，则删除该计划')}
                </span>
              </div>
            );
          }}
        </Form.Item>
        <Form.Item shouldUpdate noStyle>
          {(f) => {
            const disabled =
              !f.getFieldValue('clear_person_empty') && f.getFieldValue('clear_person_exist');
            return (
              <div style={{ display: 'flex', gap: 8, alignItems: 'center', height: '32px' }}>
                <Form.Item
                  name={'clear_person_exist'}
                  initialValue={true}
                  noStyle
                  valuePropName={'checked'}
                >
                  <Checkbox disabled={disabled} style={{ lineHeight: '32px' }} />
                </Form.Item>
                <span>
                  {I18N.t('指定时间之前创建的邀约计划，如果至少有一个达人接受了邀约计划')}
                </span>
              </div>
            );
          }}
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {(f) => {
            const disabled = !f.getFieldValue('clear_person_exist');
            return (
              <Form.Item
                name={'clear_person_exist_policy'}
                style={{ marginBottom: 0 }}
                initialValue={'keep'}
              >
                <Radio.Group
                  disabled={disabled}
                  style={{ width: '100%', overflow: 'hidden', paddingLeft: 24 }}
                >
                  <Space size={0} direction={'vertical'}>
                    <Radio style={{ lineHeight: '32px' }} value={'keep'}>
                      <Space>
                        <span>{I18N.t('维持该计划不动')}</span>
                        <HelpTooltip
                          type={'Link'}
                          title={I18N.t('不会对该计划中的达人做任何清理动作')}
                        />
                      </Space>
                    </Radio>
                    <Radio style={{ lineHeight: '32px' }} value={'by_showcase'}>
                      <Space>
                        <span>{I18N.t('保留已添加产品至橱窗的达人')}</span>
                        <HelpTooltip
                          type={'Link'}
                          title={I18N.t('凡是未添加产品至橱窗的达人全部清理')}
                        />
                      </Space>
                    </Radio>
                    <Radio style={{ lineHeight: '32px' }} value={'by_promotion'}>
                      <Space>
                        <span>{I18N.t('只保留已添加产品至橱窗且已发布内容的达人')}</span>
                        <HelpTooltip
                          type={'Link'}
                          title={I18N.t('凡是未发布相关内容的达人全部清理（即便已添加产品至橱窗）')}
                        />
                      </Space>
                    </Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
            );
          }}
        </Form.Item>
      </DMFormItem>
    </>
  );
};

export function getJobPreserveKeyByShop(jobType: API.TkshopPlanVo['jobType'], shopId?: number) {
  return `${jobType}_PARAMS_${shopId || ''}_v20250402`;
}

export type PlanTskType = 'TS_SyncShopInfo' | 'TS_IMChat' | 'TS_TargetPlan' | 'TS_SyncCreator';
export const FlowParamsByJobType = (props: {
  jobType: API.TkshopPlanVo['jobType'];
  shop: API.ShopBriefVo;
}) => {
  const { jobType, shop } = props;
  if (jobType === 'TS_SyncCreator') {
    return (
      <div style={{ paddingTop: 16 }}>
        <SyncCreatorFetchContactFormItem label={I18N.t('更新策略')} />
      </div>
    );
  }
  if (jobType === 'TS_SyncShopInfo') {
    return (
      <div style={{ paddingTop: 16 }}>
        <ShopInfoSyncActionsFormItem />
      </div>
    );
  }
  if (jobType === 'TS_TargetPlanByHandle') {
    return (
      <div style={{ paddingTop: 16 }}>
        <InviteByHandleFields refer={'schedule'} shop={shop} />
      </div>
    );
  }
  if (jobType === 'TS_TargetPlanByFilter') {
    return <InviteByFilterFields refer={'schedule'} validateMode={'max'} shop={shop!} />;
  }
  if (jobType === 'TS_IMChatByFilter') {
    return <SendMsgByFilterFields validateMode={'max'} shop={shop!} />;
  }
  if (jobType === 'TS_IMChatByHandle') {
    return (
      <div style={{ paddingTop: 16 }}>
        <SendMsgByHandleFields shop={shop!} />
      </div>
    );
  } else if (jobType === 'TS_DemandPayment') {
    return (
      <div style={{ paddingTop: 16 }}>
        <DemandPaymentFields shop={shop} />
      </div>
    );
  } else if (jobType === 'TS_TargetPlanClear') {
    return (
      <div style={{ paddingTop: 16 }}>
        <TargetPlanClearFormItems />
      </div>
    );
  }
  return (
    <EmptyView
      description={I18N.t('{{jobType}}未定义参数', {
        jobType,
      })}
    />
  );
};
export const AutoPlanTypeSelectField = (props: { value: any; onChange: any }) => {
  const { value, onChange } = props;
  return (
    <Selector
      value={value}
      allowClear={false}
      onChange={(val) => {
        onChange(val);
      }}
      options={[
        {
          label: I18N.t('达人定向邀约'),
          value: 'TS_TargetPlan',
        },
        {
          label: I18N.t('向达人发送站内消息'),
          value: 'TS_IMChat',
        },
        {
          label: I18N.t('店铺信息同步'),
          value: 'TS_SyncShopInfo',
        },
        {
          label: I18N.t('达人基础信息更新'),
          value: 'TS_SyncCreator',
        },
        {
          label: I18N.t('未付款订单催付'),
          value: 'TS_DemandPayment',
        },
        {
          label: I18N.t('清理定向邀约计划'),
          value: 'TS_TargetPlanClear',
        },
      ]}
    />
  );
};

export function transformScheduleParams(options: {
  jobType: API.TkshopPlanVo['jobType'];
  values: any;
}) {
  const { jobType, values } = options;
  const { TS_SyncShopInfo, fetch_contact, SpeechConfig, ...rest } = values || {};

  const params = {
    advanceSettings: {
      ...rest,
    },
    _params: values,
    wordsIds: getWordIdsFromSpeechConfig(SpeechConfig),
    content: getContentFromSpeechConfig(SpeechConfig),
  };

  if (jobType === 'TS_TargetPlanByFilter' || jobType === 'TS_TargetPlanByHandle') {
    // 删除endDate
    try {
      delete params.advanceSettings.endDate;
    } catch (e) {
      console.log(e);
    }
  }
  if (jobType === 'TS_SyncShopInfo') {
    delete params.wordsIds;
    delete params.content;
    params._params.ignore_buyer_contact_info = !(params._params.need_buyer_contact_info ?? true);
    params.advanceSettings = {
      ...TS_SyncShopInfo,
    };
  } else if (jobType === 'TS_SyncCreator') {
    delete params.wordsIds;
    delete params.content;
    params.advanceSettings = {
      fetch_contact,
    };
  } else if (jobType === 'TS_TargetPlanByFilter' || jobType === 'TS_IMChatByFilter') {
    // 生成唯一key
    params.advanceSettings.followerSearchKey = generateUniqueKey({
      hash_prefix: jobType === 'TS_TargetPlanByFilter' ? 'target_plan' : 'send_msg',
      ...rest,
    });
  }
  if (
    jobType === 'TS_TargetPlanByHandle' ||
    jobType === 'TS_IMChatByHandle' ||
    jobType === 'TS_TargetPlanByFilter' ||
    jobType === 'TS_IMChatByFilter'
  ) {
    // 达人标签设置
    if (!params.advanceSettings.bind_manual_tag) {
      delete params.advanceSettings.manual_tag;
    }
  }
  return params;
}

export const LayoutShopSelector = (
  props: SelectorProps & { data: API.TkshopPlanChainGroupVo[] },
) => {
  const { data, ...rest } = props;
  return (
    <Selector
      allowClear={false}
      options={data?.map((item) => {
        const { shop, id } = item;
        return {
          label: <ShopDetailNode data={shop!} />,
          value: id!,
          ...item,
        };
      })}
      {...rest}
    />
  );
};
export const SuggestionStyledCard = styled.div`
  height: 160px;
  display: flex;
  align-items: stretch;
  overflow: hidden;
  border: 1px solid #dddddd;
  border-radius: 3px;

  .left {
    display: flex;
    flex: 0 0 320px;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 12px;
    line-height: 18px;

    .header {
      color: #0f7cf4;
    }

    .content {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-evenly;
      text-align: justify;
      text-indent: 2em;
    }
  }

  .right {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    padding: 18px;
    border-left: 1px solid #dddddd;

    .plan-card {
      &:nth-of-type(1) {
        border-color: #0f7cf4;
      }

      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      justify-content: center;
      width: 140px;
      height: 75px;
      border: 1px solid #52c41a;
      border-radius: 3px;
    }

    .line {
      box-sizing: content-box;
      width: 18px;
      margin: 0 2px;
      border-bottom: 1px dashed #52c41a;
    }
    .tips {
      position: absolute;
      bottom: 0;
      left: 18px;
    }
  }
`;
export const SuggestionStyledAsidePanel = styled(SuggestionStyledCard)`
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  border-radius: 3px;
  padding: 16px !important;

  .left {
    flex: 0 0 auto !important;
    gap: 24px !important;
    padding: 0 !important;
    .content {
      gap: 16px !important;
      justify-content: flex-start !important;
    }
  }

  .right {
    flex-direction: column !important;
    padding-top: 24px;
    border-left: none !important;
    .plan-card {
      gap: 4px !important;
    }
    .line {
      width: 0 !important;
      height: 18px;
      margin: 2px 0;
      border-right: 1px dashed #52c41a;
      border-bottom: none !important;
    }
  }
`;
export const StyledSuggestionSteps = styled(Steps)`
  border-bottom: 1px solid #ddd;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 0;
  .ant-steps-item-active {
    .ant-steps-item-title {
      color: #0f7cf4 !important;
    }
  }
  .ant-steps-icon {
    .dm-colours-icon {
      width: 20px !important;
      height: 20px !important;
      font-size: 20px !important;
      vertical-align: baseline !important;
    }
  }
  .ant-steps-item-title {
    font-size: 16px !important;
    white-space: nowrap !important;
  }
`;
export const StyledSuggestionModal = styled(DMModal)`
  .ant-modal-body {
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 0;
  }
  main {
    display: flex;
    flex-direction: column;
    height: 460px;
    padding: 0 24px 0 24px;
    overflow: auto;
    scroll-behavior: smooth;
    > form {
      display: flex;
      flex: 1;
      align-items: stretch;
      overflow: hidden;
      > section {
        &:nth-of-type(1) {
          display: flex;
          flex: 1;
          flex-direction: column;
          padding-top: 24px;
          padding-right: 16px;
          overflow: auto;
        }
        &:nth-of-type(2) {
          display: flex;
          flex: 0 0 240px;
          flex-direction: column;
          gap: 12px;
          padding-top: 16px;
          padding-left: 24px;
          border-left: 1px solid #ddd;
          .title,
          .sub-title {
            color: #0f7cf4;
            text-align: center;
          }
          .description {
            padding-top: 12px;
            text-align: justify;
            text-indent: 2em;
          }
        }
      }
    }
  }
`;
