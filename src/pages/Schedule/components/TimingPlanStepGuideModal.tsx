import DMModal from '@/components/Common/Modal/DMModal';
import StepGuideContent from '@/components/StepGuide/StepGuideContent';
import I18N from '@/i18n';
import type { FormInstance } from 'antd';
import { Form, Result, Space } from 'antd';
import { Radio, Typography } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { autoOpenErrorField } from '@/hooks/interactions';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { ShopDetailNode } from '@/components/Common/ShopNode';
import { RunTimeItemCon } from '@/pages/Schedule/components/RuntimeFormItem';
import {
  tkshopPlanCreateTimingPlanPost,
  tkshopPlanUpdatePlanPost,
} from '@/services/api-TKShopAPI/TkShopPlanController';
import type { CreatorFilterType, PlanTskType } from '@/pages/Schedule/components/util';
import { FlowParamsByJobType } from '@/pages/Schedule/components/util';
import { transformScheduleParams } from '@/pages/Schedule/components/util';
import { AutoPlanTypeSelectField } from '@/pages/Schedule/components/util';
import { getJobPreserveKeyByShop, usePreserveParams } from '@/pages/Schedule/components/util';
import {
  convertCron2Time,
  convertTime2Cron,
  DaysCheckboxGroup,
  getPlanType,
} from '@/pages/Setting/components/AutoPlan';
import moment from 'moment';
import HelpTooltip from '@/components/HelpTooltip';
import {
  LiveCreatorSearchFields,
  setLocalStorageFilter,
  transformCreatorFilter,
} from '@/pages/TikTok/Live/components/CreatorAdvanceSearchModal';
import TkAreaConstants from '@/constants/TkAreaConstants';
import DMConfirm from '@/components/Common/DMConfirm';
import { getTeamIdFromUrl } from '@/utils/utils';
import _ from 'lodash';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';

const TimingPlanStepGuideModal = (
  props: GhostModalWrapperComponentProps & {
    group: API.TkshopPlanChainGroupVo;
    onUpdate: () => void;
    plan?: API.TkshopPlanVo;
    last?: API.PlanGroupChain;
    initStep?: number;
  },
) => {
  const { group, plan, onUpdate, last, initStep = 0, modalProps } = props;
  const { shop } = group;
  const [visible, setVisible] = useState(true);
  const hasAuth = useAuthJudgeCallback();
  const [expression, setExpression] = useState(() => {
    if (plan) {
      const _time = plan.expressions?.[0] || '';
      return convertCron2Time(_time);
    }
    if (last && last.plans?.[0]?.expressions?.[0]) {
      const _time = last.plans?.[0]?.expressions?.[0] || '';
      const _moment = moment(_time, 'ss mm HH').add(1, 'hours');
      if (_moment.isValid()) {
        return _moment.format('HH:mm:ss');
      }
    }
    return '07:00:00';
  });
  const [cronExpression, setCronExpression] = useState(() => {
    if (plan) {
      return plan.cronExpression?.split(',') || [];
    }
    return ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
  });
  const [taskType, setTaskType] = useState<PlanTskType>(() => {
    if (!plan) {
      return 'TS_TargetPlan';
    }
    if (plan.jobType === 'TS_TargetPlanByHandle' || plan.jobType === 'TS_TargetPlanByFilter') {
      return 'TS_TargetPlan';
    }
    return plan.jobType;
  });
  const [filterType, setFilterType] = useState<CreatorFilterType>(() => {
    if (!plan) {
      return 'ByFilter';
    }
    if (
      plan.jobType === 'TS_TargetPlanByHandle' ||
      plan.jobType === 'TS_SyncCreator' ||
      plan.jobType === 'TS_IMChatByHandle'
    ) {
      return 'ByHandle';
    }
    return 'ByFilter';
  });
  const [byHandleFilter, _setByHandleFilter] = useState<any>(() => {
    if (!plan) {
      return {
        TS_SyncCreator: {
          contactAllEmpty: true,
          regions: [TkAreaConstants.platformAreaToLocation[shop?.platform?.area!]],
        },
      };
    }
    try {
      return { [plan.jobType!]: JSON.parse(plan.creatorFilter!) };
    } catch (e) {
      return {};
    }
  });
  const [params, _setParams] = useState<any>(() => {
    if (!plan) {
      return {};
    }
    try {
      return { [plan.jobType!]: JSON.parse(plan.params!)._params };
    } catch (e) {
      return {};
    }
  });
  // @ts-ignore
  const jobType: API.TkshopPlanVo['jobType'] = useMemo(() => {
    if (plan) {
      return plan.jobType!;
    }
    if (taskType === 'TS_TargetPlan' || taskType === 'TS_IMChat') {
      return taskType + filterType;
    }
    return taskType;
  }, [plan, taskType, filterType]);
  const setParams = useCallback(
    (vals) => {
      _setParams((_prev: any) => {
        if (_prev) {
          return {
            ..._prev,
            [jobType!]: vals,
          };
        }
        return {
          [jobType!]: vals,
        };
      });
    },
    [jobType],
  );
  const setByHandleFilter = useCallback(
    (vals) => {
      _setByHandleFilter((_prev: any) => {
        if (_prev) {
          return {
            ..._prev,
            [jobType!]: vals,
          };
        }
        return {
          [jobType!]: vals,
        };
      });
    },
    [jobType],
  );

  const { read, write } = usePreserveParams(getJobPreserveKeyByShop(jobType, shop?.id));
  const { read: read2, write: write2 } = usePreserveParams(
    `${jobType}_Creator_Filter_${shop?.id || 'Common'}`,
  );
  const flowParamsFormRef = useRef<FormInstance>();
  const creatorFilterFormRef = useRef<FormInstance>();
  const showCreatorFilter = useMemo(() => {
    return (
      jobType == 'TS_IMChatByHandle' ||
      jobType === 'TS_TargetPlanByHandle' ||
      jobType === 'TS_SyncCreator'
    );
  }, [jobType]);
  useEffect(() => {
    if (!plan) {
      if (!params[jobType!]) {
        flowParamsFormRef.current?.resetFields();
        read().then((res) => {
          flowParamsFormRef.current?.setFieldsValue(res || {});
          setParams(res || {});
        });
      }
      if (showCreatorFilter) {
        if (!byHandleFilter[jobType!]) {
          creatorFilterFormRef.current?.resetFields();
          read2().then((res) => {
            creatorFilterFormRef.current?.setFieldsValue(res || {});
            setByHandleFilter(res || {});
          });
        }
      }
    }
  }, [
    byHandleFilter,
    jobType,
    params,
    plan,
    read,
    read2,
    setByHandleFilter,
    setParams,
    showCreatorFilter,
  ]);

  return (
    <DMModal
      stepGuide
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <StepGuideContent
        initStep={initStep}
        formProps={{
          preserve: true,
          requiredMark: false,
          onFinishFailed() {
            autoOpenErrorField();
          },
        }}
        guideDesc={I18N.t('选择计划类型与相应的流程脚本，当条件满足时会自动执行流程')}
        guideName={I18N.t('创建流程计划')}
        steps={[
          {
            title: I18N.t('选择流程与账号'),
            content() {
              return (
                <div style={{ paddingTop: 16 }}>
                  <DMFormItemContext.Provider value={{ labelWidth: 90 }}>
                    <DMFormItem label={I18N.t('店铺')}>
                      <ShopDetailNode data={shop!} />
                    </DMFormItem>
                    <DMFormItem label={I18N.t('流程')} shouldUpdate>
                      {plan ? (
                        getPlanType(jobType)
                      ) : (
                        <AutoPlanTypeSelectField value={taskType} onChange={setTaskType} />
                      )}
                    </DMFormItem>
                    <DMFormItem label={I18N.t('计划类型')}>
                      <Typography.Text>{I18N.t('自动计划')}</Typography.Text>
                    </DMFormItem>
                    <Form.Item shouldUpdate noStyle>
                      {() => {
                        if (
                          jobType === 'TS_SyncShopInfo' ||
                          jobType === 'TS_DemandPayment' ||
                          jobType === 'TS_TargetPlanClear'
                        ) {
                          return false;
                        }
                        if (jobType === 'TS_SyncCreator') {
                          return (
                            <DMFormItem label={I18N.t('达人来源')} shouldUpdate>
                              <Typography.Text>{I18N.t('团队达人库')}</Typography.Text>
                            </DMFormItem>
                          );
                        }
                        if (plan) {
                          return (
                            <DMFormItem label={I18N.t('达人来源')} shouldUpdate>
                              {filterType === 'ByFilter' ? (
                                <span>
                                  <Typography.Text>{I18N.t('TikTok达人市场')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的筛选条件去达人市场检索')}
                                    />
                                  </span>
                                </span>
                              ) : (
                                <span style={{ lineHeight: '32px' }}>
                                  <Typography.Text>{I18N.t('团队达人库')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的过滤条件从团队达人库检索')}
                                    />
                                  </span>
                                </span>
                              )}
                            </DMFormItem>
                          );
                        }
                        return (
                          <DMFormItem label={I18N.t('达人来源')} shouldUpdate>
                            <Radio.Group
                              value={filterType}
                              onChange={(e) => {
                                setFilterType(e.target.value);
                              }}
                            >
                              <Space>
                                <Radio value={'ByFilter'} style={{ lineHeight: '32px' }}>
                                  <Typography.Text>{I18N.t('TikTok达人市场')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的筛选条件去达人市场检索')}
                                    />
                                  </span>
                                </Radio>
                                <Radio value={'ByHandle'} style={{ lineHeight: '32px' }}>
                                  <Typography.Text>{I18N.t('团队达人库')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的过滤条件从团队达人库检索')}
                                    />
                                  </span>
                                </Radio>
                              </Space>
                            </Radio.Group>
                          </DMFormItem>
                        );
                      }}
                    </Form.Item>
                    <DMFormItem
                      label={'执行时间'}
                      name={'_expressions'}
                      rules={[
                        {
                          validator: () => {
                            if (!expression) {
                              return Promise.reject(new Error('请选择执行时间'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Form.Item noStyle shouldUpdate>
                        {() => {
                          return <RunTimeItemCon value={expression} onChange={setExpression} />;
                        }}
                      </Form.Item>
                    </DMFormItem>
                    <DMFormItem
                      label="重复日期"
                      name="_cronExpression"
                      rules={[
                        {
                          validator: () => {
                            if (cronExpression.length === 0) {
                              return Promise.reject(new Error('请选择重复日期'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Form.Item noStyle shouldUpdate>
                        {() => {
                          return (
                            <DaysCheckboxGroup
                              value={cronExpression}
                              onChange={setCronExpression}
                            />
                          );
                        }}
                      </Form.Item>
                    </DMFormItem>
                  </DMFormItemContext.Provider>
                </div>
              );
            },
            onFinish: async () => {
              return true;
            },
          },
          {
            title: I18N.t('高级查询条件'),
            hidden: !showCreatorFilter,
            formRef: creatorFilterFormRef,
            initialValues: byHandleFilter[jobType!],
            content() {
              return <LiveCreatorSearchFields initialValues={byHandleFilter[jobType!]} plan />;
            },
            extra: (
              <Typography.Link
                style={{ position: 'relative', top: 10 }}
                onClick={async () => {
                  const _values = await creatorFilterFormRef.current!.validateFields();
                  const filter = transformCreatorFilter(_values);
                  DMConfirm({
                    width: 520,
                    title: I18N.t('以当前的查询条件，预览并校验查询结果'),
                    content: I18N.t(
                      '系统会打开一个新标签页，以当前的查询条件查询团队达人库，帮助您校验查询条件是否匹配您的需求',
                    ),
                    onOk() {
                      setLocalStorageFilter(_.omit(filter, 'limit'));
                      window.open(`/team/${getTeamIdFromUrl()}/creator/store`);
                    },
                  });
                }}
              >
                {I18N.t('预览查询结果')}
              </Typography.Link>
            ),
            onFinish: async (values) => {
              const filter = transformCreatorFilter(values);
              setByHandleFilter(filter);
              return true;
            },
          },
          {
            title: I18N.t('设置流程参数'),
            initialValues: params[jobType!],
            formRef: flowParamsFormRef,
            content() {
              return <FlowParamsByJobType jobType={jobType} shop={shop!} />;
            },
            onFinish: async (values = {}) => {
              const flowParams = transformScheduleParams({ jobType, values });
              const creatorFilter = byHandleFilter[jobType!];

              if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
                showFunctionCodeAlert();
                return false;
              }
              try {
                const _commonFields: Omit<API.UpdatePlanRequest, 'id' | 'delayTime'> = {
                  cronExpression: cronExpression.join(','),
                  expressions: [convertTime2Cron(expression)],
                  creatorFilter: showCreatorFilter ? JSON.stringify(creatorFilter) : undefined,
                  params: JSON.stringify(flowParams),
                };
                if (!plan) {
                  // 创建计划
                  const _body: API.chuangjianyigetkshopdingshijihua = {
                    jobType,
                    bizScene: 'ShopCreator',
                    planGroupId: group.id,
                    ..._commonFields,
                    enabled: true,
                  };
                  await tkshopPlanCreateTimingPlanPost(_body);
                  write(flowParams._params);
                  if (showCreatorFilter) {
                    write2(creatorFilter);
                  }
                  return true;
                } else {
                  // 修改计划
                  const _body: API.UpdatePlanRequest = {
                    id: plan.id!,
                    ..._commonFields,
                  };
                  await tkshopPlanUpdatePlanPost(_body);
                  write(flowParams._params);
                  if (showCreatorFilter) {
                    write2(creatorFilter);
                  }
                  return true;
                }
              } catch (e) {
                return false;
              }
            },
          },
          {
            title: I18N.t('结果'),
            onFinish: async () => {
              setVisible(false);
              onUpdate();
              return true;
            },
            content() {
              return (
                <Result
                  status={'success'}
                  title={plan ? I18N.t('计划已修改') : I18N.t('计划已创建')}
                />
              );
            },
          },
        ]}
      />
    </DMModal>
  );
};
export default TimingPlanStepGuideModal;
