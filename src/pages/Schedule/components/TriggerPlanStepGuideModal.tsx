import DMModal from '@/components/Common/Modal/DMModal';
import StepGuideContent from '@/components/StepGuide/StepGuideContent';
import I18N from '@/i18n';
import type { FormInstance } from 'antd';
import { Form, InputNumber, Result, Space } from 'antd';
import { Radio, Typography } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { autoOpenErrorField } from '@/hooks/interactions';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { ShopDetailNode } from '@/components/Common/ShopNode';
import {
  tkshopPlanCreateFollowingPlanPost,
  tkshopPlanUpdatePlanPost,
} from '@/services/api-TKShopAPI/TkShopPlanController';
import type { PlanTskType } from '@/pages/Schedule/components/util';
import { transformScheduleParams } from '@/pages/Schedule/components/util';
import { AutoPlanTypeSelectField } from '@/pages/Schedule/components/util';
import {
  type CreatorFilterType,
  getJobPreserveKeyByShop,
  usePreserveParams,
} from '@/pages/Schedule/components/util';
import { FlowParamsByJobType } from '@/pages/Schedule/components/util';
import { getPlanType } from '@/pages/Setting/components/AutoPlan';
import {
  LiveCreatorSearchFields,
  setLocalStorageFilter,
  transformCreatorFilter,
} from '@/pages/TikTok/Live/components/CreatorAdvanceSearchModal';
import HelpTooltip from '@/components/HelpTooltip';
import TkAreaConstants from '@/constants/TkAreaConstants';
import DMConfirm from '@/components/Common/DMConfirm';
import _ from 'lodash';
import { getTeamIdFromUrl } from '@/utils/utils';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';

const TriggerPlanStepGuideModal = (
  props: GhostModalWrapperComponentProps & {
    group: API.TkshopPlanChainGroupVo;
    onUpdate: () => void;
    prev: API.TkshopPlanVo;
    next?: API.TkshopPlanVo;
    plan?: API.TkshopPlanVo;
    initStep?: number;
  },
) => {
  const { group, plan, onUpdate, prev, initStep = 0, modalProps } = props;
  const { shop } = group;
  const hasAuth = useAuthJudgeCallback();

  const [visible, setVisible] = useState(true);
  const [delayTime, setDelayTime] = useState(() => {
    if (plan) {
      return plan.delayTime;
    }
    return 5;
  });
  const [triggerPolicy, setTriggerPolicy] = useState<API.TkshopPlanVo['triggerPolicy']>(() => {
    if (plan) {
      return plan.triggerPolicy;
    }
    return 'OnSuccess';
  });
  const [taskType, setTaskType] = useState<PlanTskType>(() => {
    if (!plan) {
      return 'TS_TargetPlan';
    }
    if (plan.jobType === 'TS_TargetPlanByHandle' || plan.jobType === 'TS_TargetPlanByFilter') {
      return 'TS_TargetPlan';
    }
    return plan.jobType;
  });
  const [filterType, setFilterType] = useState<CreatorFilterType>(() => {
    if (!plan) {
      return 'ByFilter';
    }
    if (
      plan.jobType === 'TS_TargetPlanByHandle' ||
      plan.jobType === 'TS_SyncCreator' ||
      plan.jobType === 'TS_IMChatByHandle'
    ) {
      return 'ByHandle';
    }
    return 'ByFilter';
  });
  const [byHandleFilter, _setByHandleFilter] = useState<any>(() => {
    if (!plan) {
      return {
        TS_SyncCreator: {
          contactAllEmpty: true,
          regions: [TkAreaConstants.platformAreaToLocation[shop?.platform?.area!]],
        },
      };
    }
    try {
      return { [plan.jobType!]: JSON.parse(plan.creatorFilter!) };
    } catch (e) {
      return {};
    }
  });
  const [params, _setParams] = useState<any>(() => {
    if (!plan) {
      return {};
    }
    try {
      return { [plan.jobType!]: JSON.parse(plan.params!)._params };
    } catch (e) {
      return {};
    }
  });
  // @ts-ignore
  const jobType: API.TkshopPlanVo['jobType'] = useMemo(() => {
    if (plan) {
      return plan.jobType!;
    }
    if (taskType === 'TS_TargetPlan' || taskType === 'TS_IMChat') {
      return taskType + filterType;
    }
    return taskType;
  }, [plan, taskType, filterType]);
  const setParams = useCallback(
    (vals) => {
      _setParams((_prev: any) => {
        if (_prev) {
          return {
            ..._prev,
            [jobType!]: vals,
          };
        }
        return {
          [jobType!]: vals,
        };
      });
    },
    [jobType],
  );
  const setByHandleFilter = useCallback(
    (vals) => {
      _setByHandleFilter((_prev: any) => {
        if (_prev) {
          return {
            ..._prev,
            [jobType!]: vals,
          };
        }
        return {
          [jobType!]: vals,
        };
      });
    },
    [jobType],
  );
  const { read, write } = usePreserveParams(getJobPreserveKeyByShop(jobType, shop?.id));
  const { read: read2, write: write2 } = usePreserveParams(
    `${jobType}_Creator_Filter_${shop?.id || 'Common'}`,
  );
  const flowParamsFormRef = useRef<FormInstance>();
  const creatorFilterFormRef = useRef<FormInstance>();
  const showCreatorFilter = useMemo(() => {
    return (
      jobType == 'TS_IMChatByHandle' ||
      jobType === 'TS_TargetPlanByHandle' ||
      jobType === 'TS_SyncCreator'
    );
  }, [jobType]);

  useEffect(() => {
    if (!plan) {
      if (!params[jobType!]) {
        read().then((res) => {
          flowParamsFormRef.current?.resetFields();
          flowParamsFormRef.current?.setFieldsValue(res || {});
          setParams(res || {});
        });
      }
      if (showCreatorFilter) {
        if (!byHandleFilter[jobType!]) {
          read2().then((res) => {
            creatorFilterFormRef.current?.resetFields();
            creatorFilterFormRef.current?.setFieldsValue(res || {});
            setByHandleFilter(res || {});
          });
        }
      }
    }
  }, [
    byHandleFilter,
    jobType,
    params,
    plan,
    read,
    read2,
    setByHandleFilter,
    setParams,
    showCreatorFilter,
  ]);
  return (
    <DMModal
      stepGuide
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <StepGuideContent
        initStep={initStep}
        formProps={{
          preserve: true,
          requiredMark: false,
          onFinishFailed() {
            autoOpenErrorField();
          },
        }}
        guideDesc={I18N.t('选择计划类型与相应的流程脚本，当条件满足时会自动执行流程')}
        guideName={I18N.t('创建流程计划')}
        steps={[
          {
            title: I18N.t('选择流程与账号'),
            content() {
              return (
                <div style={{ paddingTop: 16 }}>
                  <DMFormItemContext.Provider value={{ labelWidth: 90 }}>
                    <DMFormItem label={I18N.t('店铺')}>
                      <ShopDetailNode data={shop!} />
                    </DMFormItem>
                    <DMFormItem label={I18N.t('流程')} shouldUpdate>
                      {plan ? (
                        getPlanType(jobType)
                      ) : (
                        <AutoPlanTypeSelectField value={taskType} onChange={setTaskType} />
                      )}
                    </DMFormItem>
                    <Form.Item shouldUpdate noStyle>
                      {() => {
                        if (
                          jobType === 'TS_SyncShopInfo' ||
                          jobType === 'TS_DemandPayment' ||
                          jobType === 'TS_TargetPlanClear'
                        ) {
                          return false;
                        }
                        if (jobType === 'TS_SyncCreator') {
                          return (
                            <DMFormItem label={I18N.t('达人来源')} shouldUpdate>
                              <Typography.Text>{I18N.t('团队达人库')}</Typography.Text>
                            </DMFormItem>
                          );
                        }
                        if (plan) {
                          return (
                            <DMFormItem label={I18N.t('达人来源')} shouldUpdate>
                              {filterType === 'ByFilter' ? (
                                <span>
                                  <Typography.Text>{I18N.t('TikTok达人市场')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的筛选条件去达人市场检索')}
                                    />
                                  </span>
                                </span>
                              ) : (
                                <span style={{ lineHeight: '32px' }}>
                                  <Typography.Text>{I18N.t('团队达人库')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的过滤条件从团队达人库检索')}
                                    />
                                  </span>
                                </span>
                              )}
                            </DMFormItem>
                          );
                        }
                        return (
                          <DMFormItem label={I18N.t('达人来源')} shouldUpdate>
                            <Radio.Group
                              value={filterType}
                              onChange={(e) => {
                                setFilterType(e.target.value);
                              }}
                            >
                              <Space>
                                <Radio value={'ByFilter'} style={{ lineHeight: '32px' }}>
                                  <Typography.Text>{I18N.t('TikTok达人市场')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的筛选条件去达人市场检索')}
                                    />
                                  </span>
                                </Radio>
                                <Radio value={'ByHandle'} style={{ lineHeight: '32px' }}>
                                  <Typography.Text>{I18N.t('团队达人库')}</Typography.Text>
                                  <span style={{ marginLeft: 16 }}>
                                    <HelpTooltip
                                      title={I18N.t('根据您设定的过滤条件从团队达人库检索')}
                                    />
                                  </span>
                                </Radio>
                              </Space>
                            </Radio.Group>
                          </DMFormItem>
                        );
                      }}
                    </Form.Item>

                    <DMFormItem label={I18N.t('计划类型')}>
                      <Typography.Text>{I18N.t('触发计划')}</Typography.Text>
                    </DMFormItem>
                    <DMFormItem label={I18N.t('前续计划')}>{getPlanType(prev.jobType)}</DMFormItem>
                    <DMFormItem label={I18N.t('触发条件')} shouldUpdate>
                      <Radio.Group
                        value={triggerPolicy}
                        onChange={(e) => {
                          setTriggerPolicy(e.target.value);
                        }}
                      >
                        <Space size={32}>
                          <Radio value={'OnSuccess'}>{I18N.t('成功结束后才触发')}</Radio>
                          <Radio value={'Always'}>{I18N.t('只要结束就触发')}</Radio>
                        </Space>
                      </Radio.Group>
                    </DMFormItem>
                    <DMFormItem
                      label={I18N.t('间隔时长')}
                      name={'_delayTime'}
                      rules={[
                        {
                          validator() {
                            if (!delayTime) {
                              return Promise.reject(new Error(I18N.t('间隔时长不能为空')));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Form.Item noStyle shouldUpdate>
                        {() => {
                          return (
                            <Space>
                              <InputNumber
                                min={1}
                                max={1200}
                                value={delayTime}
                                onChange={setDelayTime}
                                addonAfter={I18N.t('分钟')}
                              />
                              <Typography.Text type={'secondary'}>
                                （最小1分钟，最大1200分钟）
                              </Typography.Text>
                            </Space>
                          );
                        }}
                      </Form.Item>
                    </DMFormItem>
                  </DMFormItemContext.Provider>
                </div>
              );
            },
            onFinish: async () => {
              return true;
            },
          },
          {
            title: I18N.t('高级查询条件'),
            hidden: !showCreatorFilter,
            formRef: creatorFilterFormRef,
            initialValues: byHandleFilter[jobType!],
            content() {
              return <LiveCreatorSearchFields initialValues={byHandleFilter[jobType!]} plan />;
            },
            extra: (
              <Typography.Link
                style={{ position: 'relative', top: 10 }}
                onClick={async () => {
                  const _values = await creatorFilterFormRef.current!.validateFields();
                  const filter = transformCreatorFilter(_values);
                  DMConfirm({
                    width: 520,
                    title: I18N.t('以当前的查询条件，预览并校验查询结果'),
                    content: I18N.t(
                      '系统会打开一个新标签页，以当前的查询条件查询团队达人库，帮助您校验查询条件是否匹配您的需求',
                    ),
                    onOk() {
                      setLocalStorageFilter(_.omit(filter, 'limit'));
                      window.open(`/team/${getTeamIdFromUrl()}/creator/store`);
                    },
                  });
                }}
              >
                {I18N.t('预览查询结果')}
              </Typography.Link>
            ),
            onFinish: async (values) => {
              const filter = transformCreatorFilter(values);
              setByHandleFilter(filter);
              return true;
            },
          },
          {
            title: I18N.t('设置流程参数'),
            formRef: flowParamsFormRef,
            initialValues: params[jobType!],
            content() {
              return <FlowParamsByJobType jobType={jobType} shop={shop!} />;
            },
            onFinish: async (values = {}) => {
              const flowParams = transformScheduleParams({ jobType, values });
              const creatorFilter = byHandleFilter[jobType!];
              if (!hasAuth([Functions.RPA_PLAN, Functions.RPA_LIST])) {
                showFunctionCodeAlert();
                return false;
              }

              try {
                const _commonFields: Omit<
                  API.UpdatePlanRequest,
                  'id' | 'expression' | 'cronExpression'
                > = {
                  creatorFilter: showCreatorFilter ? JSON.stringify(creatorFilter) : undefined,
                  params: JSON.stringify(flowParams),
                  triggerPolicy,
                  delayTime,
                };
                if (!plan) {
                  // 创建计划
                  const _body: API.CreateTkshopFollowingPlanRequest = {
                    jobType,
                    bizScene: 'ShopCreator',
                    parentId: prev.id,
                    ..._commonFields,
                    enabled: true,
                  };
                  await tkshopPlanCreateFollowingPlanPost(_body);
                  write(flowParams._params);
                  if (showCreatorFilter) {
                    write2(creatorFilter);
                  }
                  return true;
                } else {
                  // 修改计划
                  const _body: API.UpdatePlanRequest = {
                    id: plan.id!,
                    ..._commonFields,
                  };
                  await tkshopPlanUpdatePlanPost(_body);
                  write(flowParams._params);
                  if (showCreatorFilter) {
                    write2(creatorFilter);
                  }
                  return true;
                }
              } catch (e) {
                return false;
              }
            },
          },
          {
            title: I18N.t('结果'),
            onFinish: async () => {
              setVisible(false);
              onUpdate();
              return true;
            },
            content() {
              return (
                <Result
                  status={'success'}
                  title={plan ? I18N.t('计划已修改') : I18N.t('计划已创建')}
                />
              );
            },
          },
        ]}
      />
    </DMModal>
  );
};
export default TriggerPlanStepGuideModal;
