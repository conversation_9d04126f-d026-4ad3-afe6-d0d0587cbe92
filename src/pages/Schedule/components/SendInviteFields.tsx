import {
  Col,
  Form,
  type FormItemProps,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Typography,
} from 'antd';
import I18N from '@/i18n';
import DMFormItem from '@/components/Common/DMFormItem';
import type { ReactNode } from 'react';
import { useState } from 'react';
import { useMemo } from 'react';
import { DatePickerCon } from '@/pages/RpaFlows/components/items/DatePickerCon';
import moment from 'moment';
import type { ProductItem } from '@/pages/RpaFlows/components/TkProductListField';
import TkProductListField from '@/pages/RpaFlows/components/TkProductListField';
import DmSwitch from '@/components/Switch';
import FilterGroupFiled from '@/pages/RpaFlows/components/items/FilterGroupFiled';
import { TargetPlanSplitTrigger } from '@/pages/RpaFlows/components/items/TargetPlanSplitConfig';
import InviteModeFormItem from '@/pages/RpaFlows/components/items/InviteModeFormItem';
import { useMount } from 'ahooks';
import SendModeFormItem from '@/pages/RpaFlows/components/items/SendModeFormItem';
import { tkshopCountTodayInteractionsGet } from '@/services/api-TKShopAPI/TkshopInteractionController';
import { tkshopSystemStatusGet } from '@/services/api-TKShopAPI/TkshopSystemController';
import { useRequest } from '@@/plugin-request/request';
import _ from 'lodash';
import HelpLink from '@/components/HelpLink';
import PreferredContentTypeFormItem from '@/pages/RpaFlows/components/items/PreferredContentTypeFormItem';
import MessageIntervalFormItem from '@/pages/TikTok/components/MessageIntervalFormItem';

export const PlanNameFormItem = (
  props: JSX.IntrinsicAttributes & FormItemProps<any> & { children?: ReactNode },
) => {
  return (
    <DMFormItem
      name={'planName'}
      initialValue={'plan {MMdd HH:mm}'}
      rules={[
        {
          required: true,
          whitespace: true,
          message: I18N.t('请输入计划名称'),
        },
      ]}
      tooltip={{
        title: (
          <div>
            <div>{I18N.t('在计划名称中可以使用以下表达式：')}</div>
            <div>{I18N.t('MM：月，如：01、12')}</div>
            <div>{I18N.t('dd：天，如：02、30')}</div>
            <div>{I18N.t('HH：小时，如：11、23')}</div>
            <div>{I18N.t('mm：分钟，如：06、59')}</div>
            <div style={{ whiteSpace: 'nowrap' }}>
              {I18N.t('{MMdd HH:mm}输出结果示例：1230 19:58')}
            </div>
          </div>
        ),
        overlayInnerStyle: {
          width: 270,
        },
      }}
      label={I18N.t('计划名称')}
      {...props}
    >
      <Input />
    </DMFormItem>
  );
};
export const PlanExpireTimeField = (props: {
  value?: string;
  onChange?: (value: string) => void;
  autoInit?: boolean;
}) => {
  const { value, onChange, autoInit } = props;
  useMount(() => {
    if (!value) {
      onChange?.(moment().add(1, 'months').format('YYYY-MM-DD HH:mm:ss'));
    }
    if (value && autoInit) {
      let endDate = moment(value);
      if (!endDate.isValid()) {
        endDate = moment().add(1, 'months');
      } else if (moment().diff(endDate, 'days') >= 0) {
        endDate = moment().add(1, 'months');
      }
      onChange?.(endDate.format('YYYY-MM-DD HH:mm:ss'));
    }
  });
  return (
    <DatePickerCon
      style={{ width: '100%' }}
      value={value}
      onChange={onChange}
      disabledDate={(current: any) => current && current < moment().endOf('day')}
    />
  );
};
export const ProductsFormItem = (props: { shop: API.ShopDetailVo }) => {
  const { shop } = props;
  return (
    <DMFormItem
      label="商品ID列表"
      name="productIds"
      rules={[
        {
          validator: (rule, val: ProductItem[]) => {
            const valid_products = _.filter(val || [], (item) => {
              return !!_.trim(item.productId);
            });
            if (!valid_products.length) {
              return Promise.reject(new Error(I18N.t('至少添加一个商品')));
            }
            if (valid_products.length > 100) {
              return Promise.reject(new Error(I18N.t('单次计划最多添加100个商品')));
            }
            const findIndex = val.findIndex((item) => {
              return (
                !!_.trim(item.productId) && !!item.enableAgencyCommission && !item.agencyCommission
              );
            });
            if (findIndex !== -1) {
              return Promise.reject(
                new Error(I18N.t(`第${findIndex + 1}行的商品，未填入广告佣金`)),
              );
            }
            return Promise.resolve();
          },
        },
      ]}
    >
      <TkProductListField shop={shop!} />
    </DMFormItem>
  );
};
export const ProductsCommissionFields = (props: { shop: API.ShopDetailVo }) => {
  const { shop } = props;
  return (
    <>
      <Row gutter={[8, 8]} style={{ overflow: 'hidden' }}>
        <Col span={12}>
          <PreferredContentTypeFormItem />
        </Col>
        <Col span={12}>
          <DMFormItem style={{ marginBottom: 0 }} label="免费样品" shouldUpdate>
            <div style={{ display: 'flex', gap: 8 }}>
              <DMFormItem
                name={'offerFreeSamples'}
                style={{ marginBottom: 0 }}
                valuePropName={'checked'}
                initialValue={false}
              >
                <DmSwitch style={{ width: 60 }} />
              </DMFormItem>
              <Form.Item shouldUpdate noStyle>
                {(f) => {
                  const enabled = f.getFieldValue('offerFreeSamples');
                  return (
                    <DMFormItem
                      style={{ marginBottom: 0, flex: 1 }}
                      name={'autoSampleApproval'}
                      initialValue={false}
                    >
                      <Select disabled={!enabled}>
                        <Select.Option value={true}>
                          <span>自动批准</span>
                        </Select.Option>
                        <Select.Option value={false}>
                          <span>手动审核</span>
                        </Select.Option>
                      </Select>
                    </DMFormItem>
                  );
                }}
              </Form.Item>
            </div>
          </DMFormItem>
        </Col>
        <Col span={12}>
          <DMFormItem
            label="默认标准佣金"
            name="defaultCommission"
            initialValue={12}
            rules={[{ required: true }]}
          >
            <InputNumber min={1} max={100} style={{ width: 100 }} precision={0} addonAfter={'%'} />
          </DMFormItem>
        </Col>
        <Col span={12}>
          <DMFormItem style={{ marginBottom: 0 }} label="默认广告佣金" shouldUpdate>
            <Space>
              <DMFormItem name={'defaultEnableAgencyCommission'} valuePropName={'checked'}>
                <DmSwitch style={{ width: 60 }} />
              </DMFormItem>
              <Form.Item shouldUpdate noStyle>
                {(f) => {
                  const enabled = f.getFieldValue('defaultEnableAgencyCommission');
                  return (
                    <DMFormItem
                      name={'defaultAgencyCommission'}
                      rules={[
                        {
                          validator(_rule, val) {
                            if (enabled && !val) {
                              return Promise.reject(new Error(I18N.t('请输入默认广告佣金')));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                      initialValue={5}
                    >
                      <InputNumber
                        disabled={!enabled}
                        style={{ width: 100 }}
                        min={1}
                        onBlur={(e) => {
                          if (!e.target.value) {
                            f.setFieldValue('defaultAgencyCommission', 1);
                            f.validateFields(['defaultAgencyCommission']);
                          }
                        }}
                        max={100}
                        precision={0}
                        addonAfter={'%'}
                      />
                    </DMFormItem>
                  );
                }}
              </Form.Item>
            </Space>
          </DMFormItem>
        </Col>
      </Row>
      <ProductsFormItem shop={shop!} />
    </>
  );
};
const InviteSumFormItemWithSuffix = (props: {
  shop: API.ShopDetailVo;
  refer?: 'invite' | 'message';
  validateMode?: 'max' | 'left';
}) => {
  const { shop, refer, validateMode = 'left' } = props;
  const [statusMessage, setStatusMessage] = useState<any>();
  const { data: quotaConfig } = useRequest(
    async () => {
      const _quota = await tkshopSystemStatusGet().then((res) => {
        if (refer === 'invite') {
          return res.data?.tkshopTeam?.targetPlanQuota!;
        }
        return res.data?.tkshopTeam?.imChatQuota!;
      });
      if (_quota > 0) {
        const used =
          validateMode === 'max'
            ? 0
            : await tkshopCountTodayInteractionsGet({
                shopId: shop.id!,
                interactType: refer === 'invite' ? 'target_plan' : 'im_chat',
              }).then((res) => {
                return res.data!;
              });
        return {
          quota: _quota,
          used,
          left: Math.max(_quota - used, 0),
        };
      }
      return undefined;
    },
    {
      formatResult(res) {
        return res;
      },
    },
  );
  const suffix = useMemo(() => {
    if (quotaConfig) {
      if (quotaConfig.quota > -1) {
        if (refer === 'message') {
          if (validateMode === 'left') {
            return (
              <Typography.Text type={'secondary'}>
                {I18N.t('（今天可私信{{quota}}人，还可私信{{left}}人）', {
                  quota: quotaConfig.quota,
                  left: quotaConfig.left,
                })}
              </Typography.Text>
            );
          }
          return (
            <Typography.Text type={'secondary'}>
              {I18N.t('（最多可私信{{quota}}人）', {
                quota: quotaConfig.quota,
              })}
            </Typography.Text>
          );
        }
        if (validateMode === 'left') {
          return (
            <Typography.Text type={'secondary'}>
              {I18N.t('（今天可邀约{{quota}}人，还可邀约{{left}}人）', {
                quota: quotaConfig.quota,
                left: quotaConfig.left,
              })}
            </Typography.Text>
          );
        }
        return (
          <Typography.Text type={'secondary'}>
            {I18N.t('（最多可邀约{{quota}}人）', {
              quota: quotaConfig.quota,
            })}
          </Typography.Text>
        );
      }
    }
    return false;
  }, [quotaConfig, refer, validateMode]);
  const trigger = useMemo(() => {
    if (refer === 'message' && validateMode !== 'max') {
      return <MessageIntervalFormItem />;
    }
    return false;
  }, [refer, validateMode]);
  return (
    <Form.Item shouldUpdate noStyle>
      {(f) => {
        const value = f.getFieldValue('inviteSum') || 0;
        return (
          <Form.Item
            name={'__invite_sum'}
            help={
              statusMessage && <Typography.Text type={'danger'}>{statusMessage}</Typography.Text>
            }
            rules={[
              {
                validator() {
                  if (!value) {
                    setStatusMessage('请输入达人数量');
                    return Promise.reject(new Error('请输入达人数量'));
                  }
                  if (value < 1) {
                    setStatusMessage('达人数量不能小于1');
                    return Promise.reject(new Error('达人数量不能小于1'));
                  }
                  if (quotaConfig) {
                    if (quotaConfig.quota > 0) {
                      if (quotaConfig.left === 0 || value > quotaConfig.left) {
                        if (refer === 'message') {
                          setStatusMessage(
                            <>
                              <span>超过可私信数量最大配额</span>
                              <HelpLink href={'/tkshop2/brief#diff'} style={{ marginLeft: 16 }} />
                            </>,
                          );
                          return Promise.reject(new Error(`超过可私信数量最大配额`));
                        }
                        setStatusMessage(
                          <>
                            超过可邀约数量最大配额
                            <HelpLink href={'/tkshop2/brief#diff'} style={{ marginLeft: 16 }} />
                          </>,
                        );
                        return Promise.reject(new Error(`超过可邀约数量最大配额`));
                      }
                    }
                  }
                  setStatusMessage(undefined);
                  return Promise.resolve();
                },
              },
            ]}
          >
            <div
              style={{
                display: 'inline-flex',
                alignItems: 'baseline',
                maxWidth: '100%',
                overflow: 'hidden',
                gap: 8,
              }}
            >
              <DMFormItem name="inviteSum" noStyle initialValue={500}>
                <InputNumber
                  min={1}
                  onChange={() => {
                    f.validateFields(['__invite_sum']);
                  }}
                  onBlur={(e) => {
                    if (!e.target.value) {
                      f.setFieldValue('inviteSum', 1);
                    }
                    f.validateFields(['__invite_sum']);
                  }}
                />
              </DMFormItem>
              {suffix}
              {trigger}
            </div>
          </Form.Item>
        );
      }}
    </Form.Item>
  );
};

export const CreatorFilterFields = (props: {
  shop: API.ShopDetailVo;
  refer?: 'invite' | 'message';
  validateMode?: 'max' | 'left';
}) => {
  const { shop, refer, validateMode = 'left' } = props;
  const commonFields = useMemo(() => {
    return (
      <>
        <DMFormItem label="达人数量" shouldUpdate style={{ marginBottom: 0 }}>
          <div
            style={{
              display: 'flex',
              gap: 16,
              alignItems: 'baseline',
              position: 'relative',
            }}
          >
            <InviteSumFormItemWithSuffix validateMode={validateMode} shop={shop} refer={refer} />
            {refer === 'invite' && <TargetPlanSplitTrigger />}
          </div>
        </DMFormItem>
        {refer === 'message' ? (
          <SendModeFormItem style={{ marginBottom: 0 }} />
        ) : (
          <InviteModeFormItem />
        )}
      </>
    );
  }, [validateMode, shop, refer]);
  return (
    <FilterGroupFiled shopVo={shop} refer={'target_plan'}>
      {commonFields}
    </FilterGroupFiled>
  );
};
